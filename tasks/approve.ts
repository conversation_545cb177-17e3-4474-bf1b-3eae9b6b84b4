import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { showEthersRes } from './common/tools'
import { Token } from '@/types/contracts/Token'

wrappedTask('approve', 'approve some amounts to specific account id', { filePath: path.basename(__filename) })
  .addParam('validId', 'validator id')
  .addParam('ownerId', 'owner account id')
  .addParam('spenderId', 'spender account id')
  .addParam('amount', 'approve amount')
  .setAction(async (taskArguments, hre) => {
    const validatorId = convertToHex({ hre, value: taskArguments.validId || '' })
    const ownerId = convertToHex({ hre, value: taskArguments.ownerId || '' })
    const spenderId = convertToHex({ hre, value: taskArguments.spenderId || '' })
    const amount = Number(taskArguments.amount || '')
    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner<Token>({ hre, contractName: 'Token' })

    console.log(`*** Token approve: ${amount}`)

    const receipt = await contract.approve(validatorId, ownerId, spenderId, amount, traceId)
    await receipt
      .wait()
      .then((res) => {
        showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
