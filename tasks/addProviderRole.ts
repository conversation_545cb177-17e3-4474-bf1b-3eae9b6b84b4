import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { getTime, showEthersRes } from './common/tools'
import { Provider } from '@/types/contracts/Provider'

wrappedTask('addProviderRole', 'add provider role', { filePath: path.basename(__filename) })
  .addParam('provId', 'provider id')
  .addParam('provKey', 'provider key')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })

    const traceId = convertToHex({ hre, value: 'trace1' })
    const provId = convertToHex({ hre, value: taskArguments.provId || '' })
    const provKey = taskArguments.provKey || ''

    const addrProv = new hre.ethers.Wallet(provKey).address

    const { contract } = await getContractWithSigner<Provider>({ hre, contractName: 'Provider' })
    contract.connect(kmsSigner)

    console.log(`*** add provider role: ${provId}=${addrProv}`)

    const deadline = await getTime()
    const kmsSig = await kmsSigner.sign(['bytes32', 'address', 'uint256'], [provId, addrProv, deadline])

    const receipt = await contract.addProviderRole(provId, addrProv, traceId, deadline, kmsSig)

    await receipt
      .wait()
      .then((res) => {
        showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
