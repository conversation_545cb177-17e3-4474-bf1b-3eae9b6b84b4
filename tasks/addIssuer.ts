import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { getTime, printTable, showErrorDetails, showEthersRes } from './common/tools'
import { Issuer } from '@/types/contracts/Issuer'

wrappedTask('addIssuer', 'Add issuer', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'Issuer ID')
  .addParam('bankCode', 'Bank code')
  .addParam('issuerName', 'Issuer name')
  .setAction(async (taskArguments, hre) => {
    try {
      const issuerId = convertToHex({ hre, value: taskArguments.issuerId || '' })
      const bankCode = taskArguments.bankCode || ''
      const issuerName = taskArguments.issuerName || ''
      const traceId = convertToHex({ hre, value: 'trace1' })

      console.log(`** addIssuer Parameters **\n`)
      const params = {
        issuerId,
        bankCode,
        issuerName,
      }
      printTable({ data: params })

      const kmsSigner = kmsSignerProvider({ hre })
      const { contract } = await getContractWithSigner<Issuer>({ hre, contractName: 'Issuer' })

      const deadline = await getTime()
      const kmsSig = await kmsSigner.sign(
        ['bytes32', 'uint16', 'string', 'uint256'],
        [issuerId, bankCode, issuerName, deadline],
      )

      const receipt = await contract.addIssuer(issuerId, bankCode, issuerName, traceId, deadline, kmsSig)

      await receipt
        .wait()
        .then((res) => {
          showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    } catch (error) {
      showErrorDetails({ error })
    }
  })
