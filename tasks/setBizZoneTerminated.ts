import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { showEthersRes } from './common/tools'
import { Validator } from '@/types/contracts/Validator'

wrappedTask('setBizZoneTerminated', 'Set Terminated BusinessZone Account.', {
  filePath: path.basename(__filename),
})
  .addParam('zoneId', 'zone id')
  .addParam('accountId', 'account id')
  .setAction(async (taskArguments, hre) => {
    const zoneId = taskArguments.zoneId || ''
    const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })

    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner<Validator>({ hre, contractName: 'Validator' })

    const receipt = await contract.setBizZoneTerminated(zoneId, accountId, traceId)

    await receipt
      .wait()
      .then((res) => {
        showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
