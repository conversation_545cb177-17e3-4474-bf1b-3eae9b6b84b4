import path from 'path'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { getTime, showEthersRes } from './common/tools'
import { JPYTokenTransferBridge } from '@/types/contracts/JPYTokenTransferBridge'

wrappedTask('_unregisterEscrowAcc', 'unregister escrow account', { filePath: path.basename(__filename) })
  .addParam('srcZoneId', 'src zone id')
  .addParam('dstZoneId', 'dst zone id')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })

    const srcZoneId = Number(taskArguments.srcZoneId || '')
    const dstZoneId = Number(taskArguments.dstZoneId || '')

    const { contract } = await getContractWithSigner<JPYTokenTransferBridge>({
      hre,
      contractName: 'JPYTokenTransferBridge',
    })

    const deadline = (await getTime()) + 10
    const kmsSig = await kmsSigner.sign(['uint16', 'uint256', 'uint256'], [srcZoneId, dstZoneId, deadline])

    const receipt = await contract.unregisterEscrowAccount(srcZoneId, dstZoneId, deadline, kmsSig)
    await receipt
      .wait()
      .then((res) => {
        showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
