import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'
import { Validator } from '@/types/contracts/Validator'

wrappedTask('hasValidator', 'Check if validator exists', { filePath: path.basename(__filename) })
  .addParam('validatorId', 'validator id')
  .setAction(async (taskArguments, hre) => {
    const validatorId = convertToHex({ hre, value: taskArguments.validatorId || '' })

    console.log(`** hasValidator Parameters **\n`)
    const params = {
      validatorId: validatorId,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner<Validator>({ hre, contractName: 'Validator' })

      const { success, err } = await contract.hasValidator(validatorId)

      const formattedReceipt = {
        result: success ? 'ok' : 'failed',
        reason: err,
      }

      console.log(`** hasValidator Receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
