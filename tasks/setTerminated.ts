import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails, showEthersRes } from './common/tools'
import { Validator } from '@/types/contracts/Validator'

wrappedTask('setTerminated', 'set Terminated', { filePath: path.basename(__filename) })
  .addParam('validatorId', 'validator Id')
  .addParam('accountId', 'account Id')
  .addParam('reasonCode', 'reason Code')
  .setAction(async (taskArguments, hre) => {
    try {
      const validatorId = convertToHex({ hre, value: taskArguments.validatorId || '' })
      const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })
      const reasonCode = convertToHex({ hre, value: taskArguments.reasonCode || '' })
      const traceId = convertToHex({ hre, value: 'trace1' })

      console.log(`** setTerminated Parameters **\n`)
      const params = {
        validatorId,
        accountId,
        reasonCode,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner<Validator>({ hre, contractName: 'Validator' })

      const receipt = await contract.setTerminated(validatorId, accountId, reasonCode, traceId)

      console.log(`** setTerminated Receipt Information **\n`)
      await receipt
        .wait()
        .then((res) => {
          showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    } catch (error) {
      showErrorDetails({ error })
    }
  })
