import path from 'path'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'
import { Issuer } from '@/types/contracts/Issuer'

wrappedTask('getIssuerList', 'Get list of issuers', { filePath: path.basename(__filename) })
  .addParam('limit', 'Number of issuers to retrieve')
  .addParam('offset', 'Starting position of the list')
  .setAction(async (taskArguments, hre) => {
    try {
      const { limit = 0, offset = 0 } = taskArguments

      console.log(`** getIssuerList Parameters **\n`)
      printTable({
        data: {
          limit,
          offset,
        },
      })

      const { contract } = await getContractWithSigner<Issuer>({ hre, contractName: 'Issuer' })

      const result = await contract.getIssuerList(limit, offset)

      console.log(`** Issuer List Information (Total: ${result.totalCount}) **\n`)
      result.issuers.forEach((issuer, index) => {
        console.log(`[${index + 1}]`)
        const issuerInfo = {
          issuerId: issuer.issuerId,
          name: issuer.name,
          bankCode: issuer.bankCode,
        }
        printTable({ data: issuerInfo })
      })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
