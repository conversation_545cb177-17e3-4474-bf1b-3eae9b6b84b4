import path from 'path'
import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { saveBackupToJson } from '@tasks/common/saveBackupToJson'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { Account } from '@/types/contracts/Account'
import { RenewableEnergyToken } from '@/types/contracts/renewableEnergyToken/RenewableEnergyToken'

wrappedTask('backupTokenIdsByAccountIds', 'backup all renewable token data', {
  filePath: path.basename(__filename),
}).setAction(async (_, hre) => {
  const { contract: reContract } = await getContractWithSigner<RenewableEnergyToken>({
    hre,
    contractName: 'RenewableEnergyToken',
  })
  const { contract: accountContract } = await getContractWithSigner<Account>({
    hre,
    contractName: 'Account',
  })

  console.log(`*** backup renewable token data...`)

  const sigRETokenPrams = await getBackupSignature({ hre, salt: 'getRETokensAll' })

  const tokensByAccountId: Array<any> = []
  let offset = 0
  const limit = 1000

  const totalCount = await accountContract.getAccountCount()

  while (tokensByAccountId.length != Number(totalCount)) {
    if (tokensByAccountId.length > Number(totalCount)) {
      console.error(`Error: TokenIdsByAccountIds count ${tokensByAccountId.length} is greater than total count`)
      break
    }
    // [result, count, error]
    const [tokenIdsByAccountIdsTemp, , err] = await reContract.backupTokenIdsByAccountIds(
      offset,
      limit,
      sigRETokenPrams.deadline,
      sigRETokenPrams.sig,
    )
    if (err != '') {
      console.log(
        `backup ${tokensByAccountId.length + 1} ~ ${tokensByAccountId.length + tokenIdsByAccountIdsTemp.length} failed`,
      )
      console.log('Error:', err)
      break
    } else {
      console.log(
        `backup ${tokensByAccountId.length + 1} ~ ${
          tokensByAccountId.length + tokenIdsByAccountIdsTemp.length
        } items to local`,
      )
      tokensByAccountId.push(...tokenIdsByAccountIdsTemp)
    }
    offset++
  }

  saveBackupToJson({ data: tokensByAccountId, fileName: 'tokensbyaccount', networkName: hre.network.name })
})
