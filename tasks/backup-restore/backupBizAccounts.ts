import path from 'path'
import { getBackupSignature } from '@tasks/common/getBackupSignature'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { saveBackupToJson } from '@tasks/common/saveBackupToJson'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { Account } from '@/types/contracts/Account'
import { RemigrationBackup } from '@/types/contracts/remigration/RemigrationBackup'

wrappedTask('backupBizAccounts', 'backup all business zone account data', {
  filePath: path.basename(__filename),
}).setAction(async (_, hre) => {
  const { contract: accountContract } = await getContractWithSigner<Account>({ hre, contractName: 'Account' })
  const { contract: remigrationContract } = await getContractWithSigner<RemigrationBackup>({
    hre,
    contractName: 'RemigrationBackup',
  })

  console.log(`*** backup business zone accounts data...`)
  const sigPrams = await getBackupSignature({ hre, salt: 'getBizAccountsAll' })

  const bizAccounts: Array<any> = []
  const bizAccountsExist: Array<any> = []
  let offset = 0
  const limit = 100

  const totalCount = await accountContract.getAccountCount()

  while (bizAccounts.length != Number(totalCount)) {
    if (bizAccounts.length > Number(totalCount)) {
      console.error(`Error: Accounts count ${bizAccounts.length} is greater than total count`)
      break
    }
    // [result, count, error]
    const [result, , err] = await remigrationContract.backupBusinessZoneAccounts(
      offset,
      limit,
      sigPrams.deadline,
      sigPrams.sig,
    )
    if (err != '') {
      console.log(`backup ${bizAccounts.length + 1} ~ ${bizAccounts.length + result.length} failed`)
      console.log('Error:', err)
      break
    } else {
      console.log(`backup ${bizAccounts.length + 1} ~ ${bizAccounts.length + result.length} items to local`)
      for (const key of Object.keys(result)) {
        if (result[key].bizAccountsByZoneId.length != 0) {
          bizAccountsExist.push(result[key])
        }
        bizAccounts.push(result[key])
      }
    }
    offset++
  }

  saveBackupToJson({ data: bizAccountsExist, fileName: 'bizaccounts', networkName: hre.network.name })
})
