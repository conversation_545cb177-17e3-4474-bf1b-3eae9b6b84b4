import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { getTime, showEthersRes } from './common/tools'
import * as PrivateKey from '@/privateKey'
import { Issuer } from '@/types/contracts/Issuer'

wrappedTask('forceBurnToken', 'force burn token', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'issuer id')
  .addParam('accountId', 'account id')
  .addParam('issuerKey', 'issuer key')
  .setAction(async (taskArguments, hre) => {
    const issuerId = convertToHex({ hre, value: taskArguments.issuerId || '' })
    const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })
    const issuerKey = taskArguments.issuerKey || ''
    const traceId = convertToHex({ hre, value: 'traceId' })

    const { contract } = await getContractWithSigner<Issuer>({ hre, contractName: 'Issuer' })

    console.log(`*** Token forceBurn`)
    const deadline = await getTime()
    const sig = await PrivateKey.sig(issuerKey, ['bytes32', 'bytes32', 'uint256'], [issuerId, accountId, deadline])

    const receipt = await contract.forceBurn(issuerId, accountId, traceId, deadline, sig[0])
    await receipt
      .wait()
      .then((res) => {
        showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
