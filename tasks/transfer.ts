import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import web3 from 'web3'
import { printTable, showErrorDetails, showEthersRes } from './common/tools'
import { JPYTokenTransferBridge } from '@/types/contracts/JPYTokenTransferBridge'

wrappedTask('transfer', '', {
  filePath: path.basename(__filename),
})
  .addParam('accountId', 'account id')
  .addParam('fromZoneId', 'from zone id')
  .addParam('toZoneId', 'to zone id')
  .addParam('amount', 'amount')
  .addParam('timeoutHeight', 'timeoutHeight')
  .setAction(async (args, hre) => {
    const accountId = convertToHex({ hre, value: args.accountId })
    const fromZoneId = Number(args.fromZoneId)
    const toZoneId = Number(args.toZoneId)
    const amount = Number(args.amount)
    const timeoutHeight = Number(args.timeoutHeight)
    const traceId = convertToHex({ hre, value: 'traceId' })

    const params = {
      'Account Id': accountId,
      'From ZoneId': fromZoneId,
      'To ZoneId': toZoneId,
      amount: amount,
      'timeout Height': timeoutHeight,
      'Trace ID': traceId,
    }

    console.log(`** Input parameter Information **\n`)
    printTable({ data: params })

    const toUint256HexPadded = (x) => web3.utils.padLeft(web3.utils.toHex(x), 64)

    const { contract } = await getContractWithSigner<JPYTokenTransferBridge>({
      hre,
      contractName: 'JPYTokenTransferBridge',
    })

    try {
      const receipt = await contract.transfer(accountId, fromZoneId, toZoneId, amount, timeoutHeight, traceId)
      console.log(`** JPYTokenTransferBridge.transfer receipt Information **\n`)
      const res = await receipt.wait()
      showEthersRes({ res })

      const packetSequences = toUint256HexPadded(receipt)
      console.log(packetSequences)
    } catch (error) {
      showErrorDetails({ error })
    }
  })
