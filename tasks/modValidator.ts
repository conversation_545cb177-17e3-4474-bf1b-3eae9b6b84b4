import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { kmsSignerProvider } from './common/kmsSignerProvider'
import { getTime, printTable, showErrorDetails, showEthersRes } from './common/tools'
import { Validator } from '@/types/contracts/Validator'

wrappedTask('modValidator', 'mod Validator', { filePath: path.basename(__filename) })
  .addParam('validatorId', 'validator id')
  .addParam('name', 'name')
  .addParam('validatorKey', 'validator Key')
  .setAction(async (taskArguments, hre) => {
    try {
      const kmsSigner = kmsSignerProvider({ hre })

      const validatorId = convertToHex({ hre, value: taskArguments.validatorId || '' })
      const name = convertToHex({ hre, value: taskArguments.name || '' })
      const traceId = convertToHex({ hre, value: 'trace1' })

      console.log(`** modValidator Parameters **\n`)
      const params = {
        validatorId,
        name,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner<Validator>({ hre, contractName: 'Validator' })

      const deadline = await getTime()
      const kmsSig = await kmsSigner.sign(['bytes32', 'bytes32', 'uint256'], [validatorId, name, deadline])

      const receipt = await contract.modValidator(validatorId, name, traceId, deadline, kmsSig)
      await receipt
        .wait()
        .then((res) => {
          showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    } catch (error) {
      showErrorDetails({ error })
    }
  })
