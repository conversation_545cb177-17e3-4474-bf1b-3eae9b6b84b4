import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { getTime, showEthersRes } from './common/tools'
import { Validator } from '@/types/contracts/Validator'

wrappedTask('modifyValidator', 'modify validator', {
  filePath: path.basename(__filename),
})
  .addParam('validId', 'validator id')
  .addParam('validName', 'validator name')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })

    const validId = convertToHex({ hre, value: taskArguments.validId || '' })
    const validName = convertToHex({ hre, value: taskArguments.validName || '' })
    const traceId = convertToHex({ hre, value: 'trace1' })

    const { contract } = await getContractWithSigner<Validator>({ hre, contractName: 'Validator' })
    contract.connect(kmsSigner)

    console.log(`*** validName更新: ${validName.toString(16)}`)
    const deadline = await getTime()
    const kmsSig = await kmsSigner.sign(['bytes32', 'bytes32', 'uint256'], [validId, validName, deadline])

    const receipt = await contract.modValidator(validId, validName, traceId, deadline, kmsSig)
    await receipt
      .wait()
      .then((res) => {
        showEthersRes({ res })
      })
      .catch((error) => console.log(error))
  })
