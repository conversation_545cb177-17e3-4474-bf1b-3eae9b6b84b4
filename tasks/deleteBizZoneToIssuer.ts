import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { getTime, printTable, showErrorDetails, showEthersRes } from './common/tools'
import { Issuer } from '@/types/contracts/Issuer'

wrappedTask('deleteBizZoneToIssuer', 'Delete Biz Zone To Issuer', { filePath: path.basename(__filename) })
  .addParam('issuerId', 'Issuer ID')
  .addParam('zoneId', 'Zone ID')
  .setAction(async (taskArguments, hre) => {
    try {
      const kmsSigner = kmsSignerProvider({ hre })

      const issuerId = convertToHex({ hre, value: taskArguments.issuerId || '' })
      const zoneId = Number(taskArguments.zoneId)
      const traceId = convertToHex({ hre, value: 'trace1' })

      console.log(`** deleteBizZoneToIssuer Parameters **\n`)
      const params = {
        issuerId,
        zoneId,
      }
      printTable({ data: params })

      const { contract } = await getContractWithSigner<Issuer>({ hre, contractName: 'Issuer' })

      const deadline = await getTime()
      const kmsSig = await kmsSigner.sign(['bytes32', 'uint16', 'uint256'], [issuerId, zoneId, deadline])

      const receipt = await contract.deleteBizZoneToIssuer(issuerId, zoneId, traceId, deadline, kmsSig)

      await receipt
        .wait()
        .then((res) => {
          showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    } catch (error) {
      showErrorDetails({ error })
    }
  })
