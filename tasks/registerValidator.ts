import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { kmsSignerProvider } from '@tasks/common/kmsSignerProvider'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { getTime, showEthersRes } from './common/tools'
import { Validator } from '@/types/contracts/Validator'

wrappedTask('registerValidator', 'register validator', {
  filePath: path.basename(__filename),
})
  .addParam('validId', 'validator id')
  .addParam('issuerId', 'issuer id')
  .addParam('validName', 'validator name')
  .addParam('validKey', 'validator key')
  .addParam('flag', 'flag')
  .setAction(async (taskArguments, hre) => {
    const kmsSigner = kmsSignerProvider({ hre })

    const { validKey = '', flag = '' } = taskArguments
    const validatorName = convertToHex({ hre, value: taskArguments.validName || '' })
    const validatorId = convertToHex({ hre, value: taskArguments.validId || '' })
    const issuerId = convertToHex({ hre, value: taskArguments.issuerId || '' })

    const traceId = convertToHex({ hre, value: 'trace1' })

    const addrValid = new hre.ethers.Wallet(validKey).address

    const { contract } = await getContractWithSigner<Validator>({ hre, contractName: 'Validator' })

    if (flag[0] === '1') {
      console.log(`*** validatorID登録: ${validatorId}`)
      const deadline = await getTime()
      const kmsSig = await kmsSigner.sign(
        ['bytes32', 'bytes32', 'bytes32', 'uint256'],
        [validatorId, issuerId, validatorName, deadline],
      )
      const receipt = await contract.addValidator(validatorId, issuerId, validatorName, traceId, deadline, kmsSig)
      await receipt
        .wait()
        .then((res) => {
          showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    }
    if (flag[1] === '1') {
      console.log(`*** validatorID権限登録: ${validatorId}=${addrValid}`)
      const deadline = await getTime()
      const kmsSig = await kmsSigner.sign(['bytes32', 'address', 'uint256'], [validatorId, addrValid, deadline])
      const receipt = await contract.addValidatorRole(validatorId, addrValid, traceId, deadline, kmsSig)
      await receipt
        .wait()
        .then((res) => {
          showEthersRes({ res })
        })
        .catch((error) => console.log(error))
    }
  })
