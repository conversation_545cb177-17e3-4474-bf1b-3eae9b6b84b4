import path from 'path'
import { convertToHex } from '@tasks/common/convertToHex'
import { getContractWithSigner } from '@tasks/common/getContractWithSigner'
import { wrappedTask } from '@tasks/common/taskMiddleware'
import { printTable, showErrorDetails } from './common/tools'
import { Account } from '@/types/contracts/Account'

wrappedTask('hasAccount', 'Check if account exists', { filePath: path.basename(__filename) })
  .addParam('accountId', 'account id')
  .setAction(async (taskArguments, hre) => {
    const accountId = convertToHex({ hre, value: taskArguments.accountId || '' })

    console.log(`** hasAccount Parameters **\n`)
    const params = {
      accountId: accountId,
    }
    printTable({ data: params })

    try {
      const { contract } = await getContractWithSigner<Account>({ hre, contractName: 'Account' })

      const { success, err } = await contract.hasAccount(accountId)

      const formattedReceipt = {
        result: success ? 'ok' : 'failed',
        reason: err,
      }

      console.log(`** hasAccount Receipt Information **\n`)
      printTable({ data: formattedReceipt })
    } catch (error) {
      showErrorDetails({ error })
    }
  })
