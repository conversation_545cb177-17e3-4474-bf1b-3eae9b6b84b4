import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { IssuerInstance, ProviderInstance, RenewableEnergyTokenInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { renewableEnergyTokenFunc } from '@test/RenewableEnergyToken/helpers/function'
import { RenewableEnergyTokenContractType } from '@test/RenewableEnergyToken/helpers/types'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'

describe('checkTransaction', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let renewableEnergyToken: RenewableEnergyTokenInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, renewableEnergyToken } =
        await contractFixture<RenewableEnergyTokenContractType>())
    })

    describe('Tokenが一つMintされている状態', () => {
      const pramsAccounts = Object.values(BASE.ACCOUNT)
        .filter((v) => typeof v === 'object')
        .slice(0, 20)
      let prams

      before(async () => {
        prams = pramsAccounts.map((v, i) => {
          return {
            accountId: v.ID,
            accountName: v.NAME,
            accountEoa: accounts[i].getAddress(),
            accountApprovalAll: [{ spenderId: pramsAccounts[i].ID, amount: 100 }],
          }
        })
        const deadline = await utils.getDeadline()
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await validatorFuncs.addValidatorRole({
          validator: validator,
          accounts: accounts,
          options: {
            validatorEoa: BASE.VALID.EOA_ADDRESS,
          },
        })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        for (let i = 0; i < prams.length; i++) {
          await validatorFuncs.addAccount({
            validator: validator,
            accounts: accounts,
            options: {
              validatorId: BASE.VALID.VALID0.ID,
              accountId: prams[i].accountId,
              accountName: prams[i].accountName,
            },
          })
          await issuerFuncs.addAccountRole({
            issuer: issuer,
            accounts: accounts,
            options: {
              issuerId: BASE.ISSUER.ISSUER0.ID,
              accountId: prams[i].accountId,
              deadline: deadline + 60,
            },
          })
        }
        await renewableEnergyTokenFunc.mint({
          renewableEnergyToken: renewableEnergyToken,
          tokenId: BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32,
          metadataId: BASE.REToken.TOKENS.TOKEN1.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN1.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          isLocked: false,
        })
      })

      it('Tokenが移転可能かどうか確認できること', async () => {
        const miscValue1 = utils.toBytes32('renewable')
        const miscValue2 = `${BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_STRING}`

        const result = await renewableEnergyTokenFunc.checkTransaction({
          renewableEnergyToken: renewableEnergyToken,
          sendAccountId: BASE.ACCOUNT.ACCOUNT2.ID, // send
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID, // from
          toAccountId: BASE.ACCOUNT.ACCOUNT0.ID, // to
          miscValue1: miscValue1,
          miscValue2: miscValue2,
        })
        const expected = {
          success: true,
          err: '',
        }
        utils.assertEqualForEachField(result, expected)
      })
      it('miscValue2に二つのtokenIdを含めると、二つ分のチェックが完了すること', async () => {
        // 事前にToken2とToken3をMint
        await renewableEnergyTokenFunc.mint({
          renewableEnergyToken: renewableEnergyToken,
          tokenId: BASE.REToken.TOKENS.TOKEN2.TOKEN_ID_BYTES32,
          metadataId: BASE.REToken.TOKENS.TOKEN1.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN1.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          isLocked: false,
        })
        await renewableEnergyTokenFunc.mint({
          renewableEnergyToken: renewableEnergyToken,
          tokenId: BASE.REToken.TOKENS.TOKEN3.TOKEN_ID_BYTES32,
          metadataId: BASE.REToken.TOKENS.TOKEN1.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN1.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          isLocked: false,
        })
        const sendAccountId = BASE.ACCOUNT.ACCOUNT0.ID
        const fromAccountId = BASE.ACCOUNT.ACCOUNT1.ID
        const toAccountId = BASE.ACCOUNT.ACCOUNT2.ID
        const miscValue1 = utils.toBytes32('renewable')
        const miscValue2 = `${BASE.REToken.TOKENS.TOKEN2.TOKEN_ID_STRING},${BASE.REToken.TOKENS.TOKEN3.TOKEN_ID_STRING}`
        const result = await renewableEnergyTokenFunc.checkTransaction({
          renewableEnergyToken: renewableEnergyToken,
          sendAccountId: sendAccountId,
          fromAccountId: fromAccountId,
          toAccountId: toAccountId,
          miscValue1: miscValue1,
          miscValue2: miscValue2,
        })
        const expected = {
          success: true,
          err: '',
        }

        utils.assertEqualForEachField(result, expected)
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, renewableEnergyToken } =
        await contractFixture<RenewableEnergyTokenContractType>())
    })

    describe('Tokenが一つMintされている状態', () => {
      const pramsAccounts = Object.values(BASE.ACCOUNT)
        .filter((v) => typeof v === 'object')
        .slice(0, 20)
      let prams

      before(async () => {
        prams = pramsAccounts.map((v, i) => {
          return {
            accountId: v.ID,
            accountName: v.NAME,
            accountEoa: accounts[i].getAddress(),
            accountApprovalAll: [{ spenderId: pramsAccounts[i].ID, amount: 100 }],
          }
        })
        const deadline = await utils.getDeadline()
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await validatorFuncs.addValidatorRole({
          validator: validator,
          accounts: accounts,
          options: {
            validatorEoa: BASE.VALID.EOA_ADDRESS,
          },
        })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        for (let i = 0; i < prams.length; i++) {
          await validatorFuncs.addAccount({
            validator: validator,
            accounts: accounts,
            options: {
              validatorId: BASE.VALID.VALID0.ID,
              accountId: prams[i].accountId,
              accountName: prams[i].accountName,
            },
          })
          await issuerFuncs.addAccountRole({
            issuer: issuer,
            accounts: accounts,
            options: {
              issuerId: BASE.ISSUER.ISSUER0.ID,
              accountId: prams[i].accountId,
              deadline: deadline + 60,
            },
          })
        }
        await renewableEnergyTokenFunc.mint({
          renewableEnergyToken: renewableEnergyToken,
          tokenId: BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_BYTES32,
          metadataId: BASE.REToken.TOKENS.TOKEN1.METADATA_ID,
          metadataHash: BASE.REToken.TOKENS.TOKEN1.METADATA_HASH,
          mintAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          ownerAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
          isLocked: false,
        })
      })

      it('MiscValue1が"renewable"でない場合、エラーを返却する', async () => {
        const miscValue1 = utils.toBytes32('dummy')
        const miscValue2 = `${BASE.REToken.TOKENS.TOKEN2.TOKEN_ID}`
        const result = await renewableEnergyTokenFunc.checkTransaction({
          renewableEnergyToken: renewableEnergyToken,
          sendAccountId: BASE.ACCOUNT.ACCOUNT2.ID, // send
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID, // from
          toAccountId: BASE.ACCOUNT.ACCOUNT0.ID, // to
          miscValue1: miscValue1,
          miscValue2: miscValue2,
        })
        utils.assertEqualForEachField(result, { err: ERR.RETOKEN.RETOKEN_INVALID_MISC1, success: false })
      })

      it('MiscValue2に含まれるtokenIdの要素数が100を超える場合、エラーを返却する', async () => {
        const miscValue1 = utils.toBytes32('renewable')
        // 101個のダミーtokenIdを作成
        const tokenIds = Array.from({ length: 101 }, (_, i) => `TOKEN_ID_${i + 1}`)
        // tokenIdをカンマ区切りで連結
        const miscValue2 = tokenIds.join(',')
        // const miscValue2 = `${BASE.REToken.TOKENS.TOKEN2.TOKEN_ID}`;
        const result = await renewableEnergyTokenFunc.checkTransaction({
          renewableEnergyToken: renewableEnergyToken,
          sendAccountId: BASE.ACCOUNT.ACCOUNT2.ID, // send
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID, // from
          toAccountId: BASE.ACCOUNT.ACCOUNT0.ID, // to
          miscValue1: miscValue1,
          miscValue2: miscValue2,
        })
        utils.assertEqualForEachField(result, { err: ERR.RETOKEN.RETOKEN_INVALID_MISC2, success: false })
      })

      it('MiscValue2に含まれる要素の値が32bytesを超える場合、エラーを返却する', async () => {
        const miscValue1 = utils.toBytes32('renewable')
        // 32bytesを超える文字列情報を作成(ASCIIで33文字)
        const miscValue2 = 'X'.repeat(33)
        const result = await renewableEnergyTokenFunc.checkTransaction({
          renewableEnergyToken: renewableEnergyToken,
          sendAccountId: BASE.ACCOUNT.ACCOUNT2.ID, // send
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID, // from
          toAccountId: BASE.ACCOUNT.ACCOUNT0.ID, // to
          miscValue1: miscValue1,
          miscValue2: miscValue2,
        })
        utils.assertEqualForEachField(result, { err: ERR.RETOKEN.RETOKEN_INVALID_MISC2, success: false })
      })

      it('トークンIDが存在しない場合、エラーを返却する', async () => {
        const miscValue1 = utils.toBytes32('renewable')
        const miscValue2 = `${BASE.REToken.TOKENS.TOKEN2.TOKEN_ID_STRING}`
        const result = await renewableEnergyTokenFunc.checkTransaction({
          renewableEnergyToken: renewableEnergyToken,
          sendAccountId: BASE.ACCOUNT.ACCOUNT2.ID, // send
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID, // from
          toAccountId: BASE.ACCOUNT.ACCOUNT0.ID, // to
          miscValue1: miscValue1,
          miscValue2: miscValue2,
        })
        utils.assertEqualForEachField(result, { err: ERR.RETOKEN.RETOKEN_NOT_EXIST, success: false })
      })

      it('ownerAccountIdとfromAccountIdが一致しない場合、エラーを返却する', async () => {
        const miscValue1 = utils.toBytes32('renewable')
        const miscValue2 = `${BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_STRING}`
        const result = await renewableEnergyTokenFunc.checkTransaction({
          renewableEnergyToken: renewableEnergyToken,
          sendAccountId: BASE.ACCOUNT.ACCOUNT0.ID, // send
          fromAccountId: BASE.ACCOUNT.ACCOUNT0.ID, // from
          toAccountId: BASE.ACCOUNT.ACCOUNT1.ID, // to
          miscValue1: miscValue1,
          miscValue2: miscValue2,
        })
        utils.assertEqualForEachField(result, { err: ERR.RETOKEN.RETOKEN_NOT_OWNER, success: false })
      })

      it('toAccountIdとfromAccountIdが一致する場合、エラーを返却する', async () => {
        const miscValue1 = utils.toBytes32('renewable')
        const miscValue2 = `${BASE.REToken.TOKENS.TOKEN1.TOKEN_ID}`
        const result = await renewableEnergyTokenFunc.checkTransaction({
          renewableEnergyToken: renewableEnergyToken,
          sendAccountId: BASE.ACCOUNT.ACCOUNT0.ID, // send
          fromAccountId: BASE.ACCOUNT.ACCOUNT0.ID, // from
          toAccountId: BASE.ACCOUNT.ACCOUNT0.ID, // to
          miscValue1: miscValue1,
          miscValue2: miscValue2,
        })
        utils.assertEqualForEachField(result, { err: ERR.RETOKEN.RETOKEN_FROM_TO_ARE_SAME, success: false })
      })

      it('sendAccountIdが存在しない場合、エラーを返却する', async () => {
        const miscValue1 = utils.toBytes32('renewable')
        const miscValue2 = `${BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_STRING}`
        const result = await renewableEnergyTokenFunc.checkTransaction({
          renewableEnergyToken: renewableEnergyToken,
          sendAccountId: BASE.ISSUER.ISSUER0.ID, // send
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID, // from
          toAccountId: BASE.ACCOUNT.ACCOUNT0.ID, // to
          miscValue1: miscValue1,
          miscValue2: miscValue2,
        })
        utils.assertEqualForEachField(result, { err: ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST, success: false })
      })

      it('toAccountIdが存在しない場合、エラーを返却する', async () => {
        const miscValue1 = utils.toBytes32('renewable')
        const miscValue2 = `${BASE.REToken.TOKENS.TOKEN1.TOKEN_ID_STRING}`
        const result = await renewableEnergyTokenFunc.checkTransaction({
          renewableEnergyToken: renewableEnergyToken,
          sendAccountId: BASE.ACCOUNT.ACCOUNT1.ID, // send
          fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID, // from
          toAccountId: BASE.ISSUER.ISSUER0.ID, // to
          miscValue1: miscValue1,
          miscValue2: miscValue2,
        })
        utils.assertEqualForEachField(result, { err: ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST, success: false })
      })
    })
  })
})
