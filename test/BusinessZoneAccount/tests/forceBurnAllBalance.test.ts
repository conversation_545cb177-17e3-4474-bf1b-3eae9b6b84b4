import { businessZoneAccountFuncs } from '@test/BusinessZoneAccount/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { ibcTokenFuncs } from '@test/IBCToken/helpers/function'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BusinessZoneAccountContractType } from '@test/BusinessZoneAccount/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'
// Chai global vars
declare let assert: Chai.Assert

describe('forceBurnAllBalance()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let businessZoneAccount: BusinessZoneAccountInstance
  let token: TokenInstance
  let contractManager: ContractManagerInstance
  let ibcToken: IBCTokenInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    let ibcAddress

    before(async () => {
      ;({ accounts, provider, issuer, validator, businessZoneAccount, token, contractManager, ibcToken } =
        await contractFixture<BusinessZoneAccountContractType>())
      ibcAddress = await accounts[0]
    })

    describe('issuerRole, accountが登録されている状態', () => {
      before(async () => {
        const ibcAddressString = await ibcAddress.getAddress()
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        for (const accountId of [
          BASE.ACCOUNT.ACCOUNT0.ID,
          BASE.ACCOUNT.ACCOUNT1.ID,
          BASE.ACCOUNT.ACCOUNT2.ID,
          BASE.ACCOUNT.ACCOUNT3.ID,
          BASE.ACCOUNT.ACCOUNT4.ID,
        ]) {
          await validatorFuncs.addAccount({ validator: validator, accounts: accounts, options: { accountId } })
        }
        await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: 300,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: 600,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await providerFuncs.addBizZone({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount: businessZoneAccount,
          accounts: accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator: validator,
          accounts: accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount: businessZoneAccount,
          accounts: accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
            zoneId: BASE.ZONE_ID.ID1,
          },
        })
        await validatorFuncs.setActiveBusinessAccountWithZone({
          validator: validator,
          accounts: accounts,
          options: {
            validatorId: BASE.VALID.VALID0.ID,
            zoneId: BASE.ZONE_ID.ID1,
            accountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
        await ibcTokenFuncs.transferToEscrow({
          ibcToken: ibcToken,
          from: ibcAddress,
          amount: 300,
          options: {
            fromAccountId: BASE.ACCOUNT.ACCOUNT1.ID,
            toAccountId: BASE.ACCOUNT.ACCOUNT0.ID,
          },
        })
        await issuerFuncs.setAccountStatus({
          issuer: issuer,
          accounts: accounts,
          accountStatus: BASE.STATUS.FROZEN,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })
      })

      it('BizZoneAccountの残高を強制償却できること', async () => {
        await issuerFuncs.forceBurn({
          issuer: issuer,
          accounts: accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER0.ID,
            accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          },
        })

        const result = await validatorFuncs.getAccountAll({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        const finExpect = {
          accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
          accountStatus: BASE.STATUS.FORCE_BURNED,
          balance: '0',
          reasonCode: BASE.REASON_CODE1,
          zoneId: BASE.ZONE_ID.ID0,
          zoneName: BASE.ZONE_NAME.NAME0,
          appliedAt: '0',
          registeredAt: await utils.getLatestBlockTimestamp(),
          terminatingAt: '0',
          terminatedAt: '0',
          mintLimit: '3000',
          burnLimit: '4000',
          chargeLimit: '2000',
          dischargeLimit: '4000',
          transferLimit: '1000',
          cumulativeLimit: '5000',
          cumulativeAmount: '900',
          cumulativeDate: String(await utils.getJSTDay()),
        }

        const cumulativeTransactionLimitsExpect = {
          cumulativeMintLimit: '1000',
          cumulativeMintAmount: '600',
          cumulativeBurnLimit: '1000',
          cumulativeBurnAmount: '0',
          cumulativeChargeLimit: '1000',
          cumulativeChargeAmount: '300',
          cumulativeDischargeLimit: '1000',
          cumulativeDischargeAmount: '0',
          cumulativeTransferLimit: '800',
          cumulativeTransferAmount: '0',
        }

        const bizExpect = [
          {
            accountName: BASE.ACCOUNT.ACCOUNT1.NAME,
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
            balance: '0',
            accountStatus: BASE.STATUS.FORCE_BURNED,
            appliedAt: await utils.getLatestBlockTimestamp(),
            registeredAt: await utils.getLatestBlockTimestamp(),
            terminatingAt: '0',
            terminatedAt: '0',
          },
        ]

        utils.assertEqualForEachField(result.accountDataAll, finExpect)
        utils.assertEqualForEachField(
          result.accountDataAll.cumulativeTransactionLimits,
          cumulativeTransactionLimitsExpect,
        )
        utils.assertEqualForEachField(
          result.accountDataAll.businessZoneAccounts.map((v) => {
            return {
              accountName: v.accountName,
              zoneId: v.zoneId,
              zoneName: v.zoneName,
              balance: v.balance,
              accountStatus: v.accountStatus,
              appliedAt: v.appliedAt,
              registeredAt: v.registeredAt,
              terminatingAt: v.terminatingAt,
              terminatedAt: v.terminatedAt,
            }
          }),
          bizExpect.map((v) => v),
        )
        assert.equal(result.err, '')
      })
    })
  })

  describe('準正常系', () => {
    let ibcAddress
    before(async () => {
      ibcAddress = await accounts[0]
      ;({ accounts, provider, issuer, validator, businessZoneAccount, token, contractManager, ibcToken } =
        await contractFixture<BusinessZoneAccountContractType>())
    })

    describe('issuerRole, accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        for (const accountId of [
          BASE.ACCOUNT.ACCOUNT0.ID,
          BASE.ACCOUNT.ACCOUNT1.ID,
          BASE.ACCOUNT.ACCOUNT2.ID,
          BASE.ACCOUNT.ACCOUNT3.ID,
          BASE.ACCOUNT.ACCOUNT4.ID,
        ]) {
          await validatorFuncs.addAccount({ validator: validator, accounts: accounts, options: { accountId } })
        }
      })

      it('Accountコントラクト以外から呼び出された場合、エラーがスローされること', async () => {
        await expect(businessZoneAccount.forceBurnAllBalance(BASE.ACCOUNT.ACCOUNT1.ID)).to.be.revertedWith(
          ERR.ACCOUNT.NOT_ACCOUNT_CONTRACT,
        )
      })
    })
  })
})
