import { BASE } from '@test/common/consts'
import * as utils from '@test/common/utils'
import { ContractManagerType, IbcAppType, SetContractsType, SetIbcAppType } from './types'
import privateKey from '@/privateKey'

// ライブラリとコントラクトのリンクを初回のみ実施するため、グローバル変数としてフラグを利用する
const flag = false

/**
 * contractManagerのイベントを呼ぶ関数を持つobject
 */
export const contractManagerFuncs = {
  version: ({ contractManager }: ContractManagerType) => {
    return contractManager.version()
  },
  setIbcApp: async ({ contractManager, accounts, ibcAddress, ibcAppName, options = {} }: SetIbcAppType) => {
    const { deadline, sig, eoaKey = BASE.EOA.ADMIN } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const signer = privateKey.key[eoaKey]
    const _sig = sig ?? privateKey.sig(signer, ['address', 'uint256'], [ibcAddress, _deadline])

    return contractManager.connect(accounts[0]).setIbcApp(ibcAddress, ibcAppName, _deadline, _sig[0])
  },
  setContracts: async ({ contractManager, accounts, addresses, options = {} }: SetContractsType) => {
    const { deadline, sig, eoaKey = BASE.EOA.ADMIN } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ??
      privateKey.sig(
        privateKey.key[eoaKey],
        [
          'address',
          'address',
          'address',
          'address',
          'address',
          'address',
          'address',
          'address',
          'address',
          'address',
          'address',
          'uint256',
        ],
        [...addresses, _deadline],
      )

    return contractManager.connect(accounts[9]).setContracts(
      {
        ctrlAddress: addresses[0],
        providerAddress: addresses[1],
        issuerAddress: addresses[2],
        validatorAddress: addresses[3],
        accountAddress: addresses[4],
        financialZoneAccountAddress: addresses[5],
        businessZoneAccountAddress: addresses[6],
        tokenAddress: addresses[7],
        ibcTokenAddress: addresses[8],
        financialCheckAddress: addresses[9],
        transferProxyAddress: addresses[10],
      },
      _deadline,
      _sig[0],
    )
  },
  accessCtrl: ({ contractManager }: ContractManagerType) => {
    return contractManager.accessCtrl()
  },
  provider: ({ contractManager }: ContractManagerType) => {
    return contractManager.getFunction('provider')
  },
  account: ({ contractManager }: ContractManagerType) => {
    return contractManager.account()
  },
  financialZoneAccount: ({ contractManager }: ContractManagerType) => {
    return contractManager.financialZoneAccount()
  },
  businessZoneAccount: ({ contractManager }: ContractManagerType) => {
    return contractManager.businessZoneAccount()
  },
  validator: ({ contractManager }: ContractManagerType) => {
    return contractManager.validator()
  },
  issuer: ({ contractManager }: ContractManagerType) => {
    return contractManager.issuer()
  },
  token: ({ contractManager }: ContractManagerType) => {
    return contractManager.token()
  },
  ibcToken: ({ contractManager }: ContractManagerType) => {
    return contractManager.ibcToken()
  },
  financialCheck: ({ contractManager }: ContractManagerType) => {
    return contractManager.financialCheck()
  },
  transferProxy: ({ contractManager }: ContractManagerType) => {
    return contractManager.transferProxy()
  },
  ibcApp: ({ contractManager, ibcAppName }: IbcAppType) => {
    return contractManager.ibcApp(ibcAppName)
  },
}
