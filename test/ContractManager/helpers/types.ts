import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccessCtrlInstance,
  AccountInstance,
  BusinessZoneAccountInstance,
  ContractCallOption,
  ContractManagerInstance,
  FinancialCheckInstance,
  FinancialZoneAccountInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  TransferProxyInstance,
  ValidatorInstance,
} from '@test/common/types'

export type ContractManagerContractType = {
  accounts: SignerWithAddress[]
  contractManager: ContractManagerInstance
  accessCtrl: AccessCtrlInstance
  provider: ProviderInstance
  issuer: IssuerInstance
  validator: ValidatorInstance
  account: AccountInstance
  financialZoneAccount: FinancialZoneAccountInstance
  businessZoneAccount: BusinessZoneAccountInstance
  token: TokenInstance
  financialCheck: FinancialCheckInstance
  transferProxy: TransferProxyInstance
  ibcToken: IBCTokenInstance
}

export type ContractManagerType = { contractManager: ContractManagerInstance }

export type BaseAccountsType = ContractManagerType & {
  accounts: SignerWithAddress[]
  options?: Partial<ContractCallOption>
}

export type SetIbcAppType = BaseAccountsType & {
  ibcAddress: string
  ibcAppName: string
}

export type SetContractsType = BaseAccountsType & {
  addresses: string[]
}

export type IbcAppType = ContractManagerType & { ibcAppName: string }
