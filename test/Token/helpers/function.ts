import { BASE } from '@test/common/consts'
import { EventReturnType } from '@test/common/types'
import * as utils from '@test/common/utils'
import {
  ApproveType,
  BurnCancelType,
  BurnType,
  CheckApproveType,
  CustomTransferType,
  GetAllowanceListType,
  GetAllowanceType,
  GetBalanceListType,
  GetTokenType,
  HasTokenType,
  MintType,
  SetTokenAllType,
  SetTokenEnabledType,
  TokenType,
  TransferSingleType,
} from './types'
import privateKey from '@/privateKey'

/**
 * tokenのイベントを呼ぶ関数を持つobject
 */
export const tokenFuncs = {
  version: ({ token }: TokenType) => {
    return token.version()
  },
  // getTotalSupply: (token: TokenInstance, ...params: Parameters<TokenInstance['getTotalSupply']>) => {
  //   return token.getTotalSupply(...params) as unknown as Promise<EventReturnType['Token']['GetTotalSupply']>;
  // },
  getAllowance: ({ token, prams }: GetAllowanceType) => {
    return token.getAllowance(...prams) as unknown as Promise<EventReturnType['Token']['GetAllowance']>
  },
  getAllowanceList: ({ token, prams }: GetAllowanceListType) => {
    return token.getAllowanceList(...prams) as unknown as Promise<EventReturnType['Token']['GetAllowanceList']>
  },
  getBalanceList: ({ token, prams }: GetBalanceListType) => {
    return token.getBalanceList(...prams) as unknown as Promise<EventReturnType['Token']['GetBalanceList']>
  },
  hasToken: ({ token, prams }: HasTokenType) => {
    return token.hasToken(...prams) as unknown as Promise<EventReturnType['Token']['HasToken']>
  },

  checkApprove: async ({ token, validatorId, ownerId, spenderId, amount, sigInfo, options = {} }: CheckApproveType) => {
    const { accountSignature, sig, deadline, eoaKey = BASE.EOA.VALID1 } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ??
      privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'bytes32', 'uint256'], [validatorId, ownerId, _deadline])
    const _accountSignature =
      accountSignature ??
      privateKey.sig(
        sigInfo.signer,
        ['bytes32', 'bytes32', 'uint256', 'uint256'],
        [ownerId, spenderId, amount, BASE.ACCOUNT_SIG_MSG.APPROVE],
      )[0]

    return token.checkApprove(
      validatorId,
      ownerId,
      spenderId,
      amount,
      _accountSignature,
      sigInfo.info,
      _deadline,
      _sig[0],
    ) as unknown as Promise<EventReturnType['FinancialCheck']['CheckApprove']>
  },
  getToken: ({ token, prams }: GetTokenType) => {
    return token.getToken(...prams) as unknown as Promise<EventReturnType['Token']['GetToken']>
  },
  setTokenEnabled: async ({ token, accounts, enabled, options = {} }: SetTokenEnabledType) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.PROV1,
      providerId = BASE.PROV.PROV0.ID,
      tokenId = BASE.TOKEN.TOKEN1.ID,
    } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const signer = privateKey.key[eoaKey]
    const _sig =
      sig ??
      privateKey.sig(signer, ['bytes32', 'bytes32', 'bool', 'uint256'], [providerId, tokenId, enabled, _deadline])

    return token.connect(accounts[0]).setTokenEnabled(providerId, tokenId, enabled, BASE.TRACE_ID, _deadline, _sig[0])
  },
  approve: async ({ token, accounts, spenderId, amount, options = {} }: ApproveType) => {
    const { from, validatorId = BASE.VALID.VALID1.ID, ownerId = BASE.ACCOUNT.ACCOUNT1.ID } = options
    const _from = from ?? accounts[0]
    return token.connect(_from).approve(validatorId, ownerId, spenderId, amount, BASE.TRACE_ID)
  },
  mint: async ({ token, accounts, amount, options = {} }: MintType) => {
    const { from, issuerId = BASE.ISSUER.ISSUER0.ID, accountId = BASE.ACCOUNT.ACCOUNT1.ID } = options
    const _from = from ?? accounts[0]
    return token.connect(_from).mint(issuerId, accountId, amount, BASE.TRACE_ID)
  },
  burn: async ({ token, accounts, amount, options = {} }: BurnType) => {
    const { issuerId = BASE.ISSUER.ISSUER0.ID, accountId = BASE.ACCOUNT.ACCOUNT1.ID } = options
    return token.connect(accounts[0]).burn(issuerId, accountId, amount, BASE.TRACE_ID)
  },
  burnCancel: async ({ token, accounts, amount, blockTimestamp, options = {} }: BurnCancelType) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ISSUER1,
      issuerId = BASE.ISSUER.ISSUER0.ID,
      accountId = BASE.ACCOUNT.ACCOUNT1.ID,
    } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const signer = privateKey.key[eoaKey]
    const _sig =
      sig ??
      privateKey.sig(
        signer,
        ['bytes32', 'bytes32', 'uint256', 'uint256', 'uint256'],
        [issuerId, accountId, amount, blockTimestamp, _deadline],
      )

    return token
      .connect(accounts[0])
      .burnCancel(issuerId, accountId, amount, blockTimestamp, BASE.TRACE_ID, _deadline, _sig[0])
  },
  transferSingle: async ({ token, accounts, amount, miscValue1, miscValue2, options = {} }: TransferSingleType) => {
    const {
      sendAccountId = BASE.ACCOUNT.ACCOUNT0.ID,
      fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID,
      toAccountId = BASE.ACCOUNT.ACCOUNT1.ID,
    } = options
    return token
      .connect(accounts[0])
      .transferSingle(
        sendAccountId,
        fromAccountId,
        toAccountId,
        amount,
        miscValue1,
        miscValue2,
        BASE.MEMO,
        BASE.TRACE_ID,
      )
  },
  customTransfer: async ({ token, accounts, amount, miscValue1, miscValue2, options = {} }: CustomTransferType) => {
    const {
      sendAccountId = BASE.ACCOUNT.ACCOUNT0.ID,
      fromAccountId = BASE.ACCOUNT.ACCOUNT0.ID,
      toAccountId = BASE.ACCOUNT.ACCOUNT1.ID,
    } = options
    return token.customTransfer(
      sendAccountId,
      fromAccountId,
      toAccountId,
      amount,
      miscValue1,
      miscValue2,
      BASE.MEMO,
      BASE.TRACE_ID,
    )
  },
  getTokenAll: async ({ token }: TokenType) => {
    return token.getTokenAll() as unknown as Promise<EventReturnType['Token']['GetToken']>
  },
  setTokenAll: async ({ token, prams, options = {} }: SetTokenAllType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [BASE.SALTS.SET_TOKEN_ALL, _deadline])
    return token.setTokenAll(prams, _deadline, _sig[0])
  },
}
