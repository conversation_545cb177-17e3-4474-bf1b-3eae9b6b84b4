import '@nomicfoundation/hardhat-chai-matchers'
import { anyValue } from '@nomicfoundation/hardhat-chai-matchers/withArgs'
import '@nomicfoundation/hardhat-ethers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { time } from '@nomicfoundation/hardhat-network-helpers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { FinancialZoneAccountInstance, IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { financialZoneAccountFuncs } from '@test/FinancialZoneAccount/helpers/function'
import { FinancialZoneAccountContractType } from '@test/FinancialZoneAccount/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('syncBurn()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let financialZoneAccount: FinancialZoneAccountInstance
  let accounts: SignerWithAddress[]

  // TODO: TokenコントラクトにaddCumlativeAmount()関数実装後修正する
  const cumulativeAmount = 200

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, financialZoneAccount } =
        await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('cumulativeAmountが0の状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider: provider, accounts: accounts })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await validatorFuncs.addValidatorRole({ validator: validator, accounts: accounts })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })
      })

      it('日付が変わっていない場合、cumulativeAmountが加算されること', async () => {
        const jstDay = await utils.getJSTDay()
        const amount = 100

        const beforeResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        const tx = await financialZoneAccountFuncs.syncBurn({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
        })

        const expectParams = {
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          amount: amount,
          cumulativeAmount: Number(beforeResult.accountData.cumulativeAmount) + amount,
          cumulativeBurnAmount:
            Number(beforeResult.accountData.cumulativeTransactionLimits.cumulativeBurnAmount) + amount,
          traceId: BASE.TRACE_ID,
        }
        await expect(tx)
          .to.emit(financialZoneAccount, 'SyncBurn')
          .withArgs(
            expectParams.accountId,
            expectParams.amount,
            anyValue,
            expectParams.cumulativeAmount,
            expectParams.cumulativeBurnAmount,
            expectParams.traceId,
          )
        const afterResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        assert.equal(Number(afterResult.accountData.cumulativeDate), jstDay)
        assert.equal(
          Number(afterResult.accountData.cumulativeAmount),
          Number(beforeResult.accountData.cumulativeAmount) + amount,
        )
      })

      it('日付が変わっていない場合、cumulativeBurnAmountが加算されること', async () => {
        const jstDay = await utils.getJSTDay()
        const amount = 100

        const beforeResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        const tx = await financialZoneAccountFuncs.syncBurn({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
        })

        const expectParams = {
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          amount: amount,
          cumulativeAmount: Number(beforeResult.accountData.cumulativeAmount) + amount,
          cumulativeBurnAmount:
            Number(beforeResult.accountData.cumulativeTransactionLimits.cumulativeBurnAmount) + amount,
          traceId: BASE.TRACE_ID,
        }
        await expect(tx)
          .to.emit(financialZoneAccount, 'SyncBurn')
          .withArgs(
            expectParams.accountId,
            expectParams.amount,
            anyValue,
            expectParams.cumulativeAmount,
            expectParams.cumulativeBurnAmount,
            expectParams.traceId,
          )
        const afterResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        assert.equal(Number(afterResult.accountData.cumulativeDate), jstDay)
        assert.equal(
          Number(afterResult.accountData.cumulativeTransactionLimits.cumulativeBurnAmount),
          Number(beforeResult.accountData.cumulativeTransactionLimits.cumulativeBurnAmount) + amount,
        )
      })

      it('日跨りで一日の累積限度額を2回目の償却で超えている場合、cumulativeAmountがリセットされること', async () => {
        const amount = 3000
        await financialZoneAccountFuncs.syncBurn({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
        })
        const beforeResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        assert.equal(Number(beforeResult.accountData.cumulativeAmount), amount + 200)

        // 1日（24時間）時間を進める
        await time.increase(24 * 60 * 60)
        const mintTx = await financialZoneAccountFuncs.syncBurn({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
        })

        const expectParams = {
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          amount: amount,
          cumulativeAmount: amount,
          cumulativeBurnAmount: amount,
          traceId: BASE.TRACE_ID,
        }
        await expect(mintTx)
          .to.emit(financialZoneAccount, 'SyncBurn')
          .withArgs(
            expectParams.accountId,
            expectParams.amount,
            anyValue,
            expectParams.cumulativeAmount,
            expectParams.cumulativeBurnAmount,
            expectParams.traceId,
          )
        const afterResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        assert.equal(Number(afterResult.accountData.cumulativeAmount), amount)
      })

      it('日跨りで一日の累積限度額を2回目の償却で超えている場合、cumulativeBurnAmountがリセットされること', async () => {
        const amount = 300
        await financialZoneAccountFuncs.syncBurn({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
        })
        const beforeResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        assert.equal(Number(beforeResult.accountData.cumulativeAmount), amount + 3000)

        // 1日（24時間）時間を進める
        await time.increase(24 * 60 * 60)
        const jstDay = await utils.getJSTDay()

        const mintTx = await financialZoneAccountFuncs.syncBurn({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
        })
        const expectParams = {
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          amount: amount,
          cumulativeAmount: amount,
          cumulativeBurnAmount: amount,
          traceId: BASE.TRACE_ID,
        }
        await expect(mintTx)
          .to.emit(financialZoneAccount, 'SyncBurn')
          .withArgs(
            expectParams.accountId,
            expectParams.amount,
            anyValue,
            expectParams.cumulativeAmount,
            expectParams.cumulativeBurnAmount,
            expectParams.traceId,
          )
        const afterResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        assert.equal(Number(afterResult.accountData.cumulativeDate), jstDay)
        assert.equal(Number(afterResult.accountData.cumulativeTransactionLimits.cumulativeBurnAmount), amount)
      })

      it('日付が変った場合、Burn実行後にcumulativeBurnAmountが加算され、cumulativeMintAmountがリセットされること', async () => {
        // Move to the next day (add 24 hours)
        await time.increase(24 * 60 * 60)
        const mintAmount = 200
        const burnAmount = 50

        // First day: Perform both mint and burn operations
        await financialZoneAccountFuncs.syncMint({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, mintAmount, BASE.TRACE_ID],
        })

        await financialZoneAccountFuncs.syncBurn({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, burnAmount, BASE.TRACE_ID],
        })

        // Verify both mint and burn amounts are set correctly after first day operations
        const firstDayResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        assert.equal(Number(firstDayResult.accountData.cumulativeTransactionLimits.cumulativeMintAmount), mintAmount)
        assert.equal(Number(firstDayResult.accountData.cumulativeTransactionLimits.cumulativeBurnAmount), burnAmount)

        // Move to the next day (add 24 hours)
        await time.increase(24 * 60 * 60)
        const jstDay = await utils.getJSTDay()

        // Second day: Perform only burn operation
        const newBurnAmount = 100
        const tx = await financialZoneAccountFuncs.syncBurn({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, newBurnAmount, BASE.TRACE_ID],
        })

        const expectParams = {
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          amount: newBurnAmount,
          cumulativeAmount: newBurnAmount,
          cumulativeBurnAmount: newBurnAmount,
          traceId: BASE.TRACE_ID,
        }

        await expect(tx)
          .to.emit(financialZoneAccount, 'SyncBurn')
          .withArgs(
            expectParams.accountId,
            expectParams.amount,
            anyValue,
            expectParams.cumulativeAmount,
            expectParams.cumulativeBurnAmount,
            expectParams.traceId,
          )

        // Verify that after the day change and burn operation:
        // 1. cumulativeBurnAmount is reset and set to the new burn amount
        // 2. cumulativeMintAmount is reset to 0
        const secondDayResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        assert.equal(Number(secondDayResult.accountData.cumulativeDate), jstDay)
        assert.equal(Number(secondDayResult.accountData.cumulativeAmount), newBurnAmount)
        assert.equal(
          Number(secondDayResult.accountData.cumulativeTransactionLimits.cumulativeBurnAmount),
          newBurnAmount,
        )
        assert.equal(Number(secondDayResult.accountData.cumulativeTransactionLimits.cumulativeMintAmount), 0)
      })
    })
  })
})
