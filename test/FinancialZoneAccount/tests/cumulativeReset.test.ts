import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  CumulativeResetOption,
  FinancialZoneAccountInstance,
  IssuerInstance,
  ProviderInstance,
  ValidatorInstance,
} from '@test/common/types'
import { financialZoneAccountFuncs } from '@test/FinancialZoneAccount/helpers/function'
import { FinancialZoneAccountContractType } from '@test/FinancialZoneAccount/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { assert, expect } from 'chai'
import { before } from 'mocha'

describe('cumulativeReset()', () => {
  let financialZoneAccount: FinancialZoneAccountInstance

  describe('準正常系', () => {
    before(async () => {
      ;({ financialZoneAccount } = await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('caller is not valid', () => {
      it('should revert when caller is not issuer contract', async () => {
        await expect(financialZoneAccount.cumulativeReset(BASE.ACCOUNT.ACCOUNT0.ID)).to.be.revertedWith(
          ERR.ISSUER.NOT_ISSUER_CONTRACT,
        )
      })
    })
  })
})

describe('cumulativeReset()', () => {
  let financialZoneAccount: FinancialZoneAccountInstance
  describe('準正常系', () => {
    before(async () => {
      ;({ financialZoneAccount } = await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('caller is not valid', () => {
      it('should revert when caller is not issuer contract', async () => {
        await expect(financialZoneAccount.cumulativeReset(BASE.ACCOUNT.ACCOUNT0.ID)).to.be.revertedWith(
          ERR.ISSUER.NOT_ISSUER_CONTRACT,
        )
      })

      it('transferLimitが999,999,999,999を超える場合、エラーがスローされること', async () => {
        await expect(
          financialZoneAccountFuncs.addAccountLimit({
            finAccount: financialZoneAccount,
            params: [
              BASE.ACCOUNT.ACCOUNT1.ID,
              {
                mint: 1000,
                burn: 2000,
                charge: 3000,
                discharge: 4000,
                transfer: *************,
                cumulative: {
                  total: 500,
                  mint: 100,
                  burn: 100,
                  charge: 100,
                  discharge: 100,
                  transfer: 100,
                },
              },
              BASE.TRACE_ID,
            ],
          }),
        ).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_TRANSFER_LIMIT)
      })

      it('chargeLimitが999,999,999,999を超える場合、エラーがスローされること', async () => {
        await expect(
          financialZoneAccountFuncs.addAccountLimit({
            finAccount: financialZoneAccount,
            params: [
              BASE.ACCOUNT.ACCOUNT1.ID,
              {
                mint: 1000,
                burn: 2000,
                charge: *************,
                discharge: 4000,
                transfer: 1000,
                cumulative: {
                  total: 500,
                  mint: 100,
                  burn: 100,
                  charge: 100,
                  discharge: 100,
                  transfer: 100,
                },
              },
              BASE.TRACE_ID,
            ],
          }),
        ).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_CHARGE_LIMIT)
      })

      it('mintLimitが999,999,999,999を超える場合、エラーがスローされること', async () => {
        await expect(
          financialZoneAccountFuncs.addAccountLimit({
            finAccount: financialZoneAccount,
            params: [
              BASE.ACCOUNT.ACCOUNT1.ID,
              {
                mint: *************,
                burn: 2000,
                charge: 1000,
                discharge: 4000,
                transfer: 1000,
                cumulative: {
                  total: 500,
                  mint: 100,
                  burn: 100,
                  charge: 100,
                  discharge: 100,
                  transfer: 100,
                },
              },
              BASE.TRACE_ID,
            ],
          }),
        ).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_MINT_LIMIT)
      })

      it('burnLimitが999,999,999,999を超える場合、エラーがスローされること', async () => {
        await expect(
          financialZoneAccountFuncs.addAccountLimit({
            finAccount: financialZoneAccount,
            params: [
              BASE.ACCOUNT.ACCOUNT1.ID,
              {
                mint: 1000,
                burn: *************,
                charge: 1000,
                discharge: 4000,
                transfer: 1000,
                cumulative: {
                  total: 500,
                  mint: 100,
                  burn: 100,
                  charge: 100,
                  discharge: 100,
                  transfer: 100,
                },
              },
              BASE.TRACE_ID,
            ],
          }),
        ).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_BURN_LIMIT)
      })

      it('cumulativeLimitが999,999,999,999,999を超える場合、エラーがスローされること', async () => {
        await expect(
          financialZoneAccountFuncs.addAccountLimit({
            finAccount: financialZoneAccount,
            params: [
              BASE.ACCOUNT.ACCOUNT1.ID,
              {
                mint: 1000,
                burn: 1000,
                charge: 1000,
                discharge: 4000,
                transfer: 1000,
                cumulative: {
                  total: ****************,
                  mint: 100,
                  burn: 100,
                  charge: 100,
                  discharge: 100,
                  transfer: 100,
                },
              },
              BASE.TRACE_ID,
            ],
          }),
        ).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_DAILY_LIMIT)
      })

      it('dischargeLimitが999,999,999,999を超える場合、エラーがスローされること', async () => {
        await expect(
          financialZoneAccountFuncs.addAccountLimit({
            finAccount: financialZoneAccount,
            params: [
              BASE.ACCOUNT.ACCOUNT1.ID,
              {
                mint: 1000,
                burn: 2000,
                charge: 3000,
                discharge: *************,
                transfer: 4000,
                cumulative: {
                  total: 500,
                  mint: 100,
                  burn: 100,
                  charge: 100,
                  discharge: 100,
                  transfer: 100,
                },
              },
              BASE.TRACE_ID,
            ],
          }),
        ).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_DISCHARGE_LIMIT)
      })

      it('cumulativeMintLimitが999,999,999,999,999を超える場合、エラーがスローされること', async () => {
        await expect(
          financialZoneAccountFuncs.addAccountLimit({
            finAccount: financialZoneAccount,
            params: [
              BASE.ACCOUNT.ACCOUNT1.ID,
              {
                mint: 1000,
                burn: 1000,
                charge: 1000,
                discharge: 4000,
                transfer: 1000,
                cumulative: {
                  total: 10000,
                  mint: ****************,
                  burn: 100,
                  charge: 100,
                  discharge: 100,
                  transfer: 100,
                },
              },
              BASE.TRACE_ID,
            ],
          }),
        ).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_DAILY_MINT_LIMIT)
      })

      it('cumulativeBurnLimitが999,999,999,999,999を超える場合、エラーがスローされること', async () => {
        await expect(
          financialZoneAccountFuncs.addAccountLimit({
            finAccount: financialZoneAccount,
            params: [
              BASE.ACCOUNT.ACCOUNT1.ID,
              {
                mint: 1000,
                burn: 1000,
                charge: 1000,
                discharge: 4000,
                transfer: 1000,
                cumulative: {
                  total: 10000,
                  mint: 100,
                  burn: ****************,
                  charge: 100,
                  discharge: 100,
                  transfer: 100,
                },
              },
              BASE.TRACE_ID,
            ],
          }),
        ).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_DAILY_BURN_LIMIT)
      })

      it('cumulativeChargeLimitが999,999,999,999,999を超える場合、エラーがスローされること', async () => {
        await expect(
          financialZoneAccountFuncs.addAccountLimit({
            finAccount: financialZoneAccount,
            params: [
              BASE.ACCOUNT.ACCOUNT1.ID,
              {
                mint: 1000,
                burn: 1000,
                charge: 1000,
                discharge: 4000,
                transfer: 1000,
                cumulative: {
                  total: 10000,
                  mint: 100,
                  burn: 100,
                  charge: ****************,
                  discharge: 100,
                  transfer: 100,
                },
              },
              BASE.TRACE_ID,
            ],
          }),
        ).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_DAILY_CHARGE_LIMIT)
      })

      it('cumulativeDischargeLimitが999,999,999,999,999を超える場合、エラーがスローされること', async () => {
        await expect(
          financialZoneAccountFuncs.addAccountLimit({
            finAccount: financialZoneAccount,
            params: [
              BASE.ACCOUNT.ACCOUNT1.ID,
              {
                mint: 1000,
                burn: 1000,
                charge: 1000,
                discharge: 4000,
                transfer: 1000,
                cumulative: {
                  total: 10000,
                  mint: 100,
                  burn: 100,
                  charge: 100,
                  discharge: ****************,
                  transfer: 100,
                },
              },
              BASE.TRACE_ID,
            ],
          }),
        ).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_DAILY_DISCHARGE_LIMIT)
      })

      it('cumulativeTransferLimitが999,999,999,999,999を超える場合、エラーがスローされること', async () => {
        await expect(
          financialZoneAccountFuncs.addAccountLimit({
            finAccount: financialZoneAccount,
            params: [
              BASE.ACCOUNT.ACCOUNT1.ID,
              {
                mint: 1000,
                burn: 1000,
                charge: 1000,
                discharge: 4000,
                transfer: 1000,
                cumulative: {
                  total: 10000,
                  mint: 100,
                  burn: 100,
                  charge: 100,
                  discharge: 100,
                  transfer: ****************,
                },
              },
              BASE.TRACE_ID,
            ],
          }),
        ).to.be.revertedWith(ERR.FINACCOUNT.EXCEEDED_DAILY_TRANSFER_LIMIT)
      })
    })
  })
})

describe('check result after call cumulativeReset()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let financialZoneAccount: FinancialZoneAccountInstance
  let accounts: SignerWithAddress[]

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, financialZoneAccount } =
        await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('check cumulative reset', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider: provider, accounts: accounts })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await validatorFuncs.addValidatorRole({ validator: validator, accounts: accounts })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })
      })

      it('compare result before and after call cumulativeReset', async () => {
        const amount = 100

        // Helper function to check cumulative values
        const assertCumulativeValues = (result, expected) => {
          assert.equal(Number(result.accountData.cumulativeAmount), Number(expected.cumulativeAmount))

          const limits = result.accountData.cumulativeTransactionLimits
          assert.equal(Number(limits.cumulativeMintAmount), Number(expected.cumulativeMintAmount))
          assert.equal(Number(limits.cumulativeBurnAmount), Number(expected.cumulativeBurnAmount))
          assert.equal(Number(limits.cumulativeChargeAmount), Number(expected.cumulativeChargeAmount))
          assert.equal(Number(limits.cumulativeDischargeAmount), Number(expected.cumulativeDischargeAmount))
          assert.equal(Number(limits.cumulativeTransferAmount), Number(expected.cumulativeTransferAmount))
        }

        // STEP 1: Check initial values
        const beforeResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        const zeroValues = {
          cumulativeAmount: 0,
          cumulativeMintAmount: 0,
          cumulativeBurnAmount: 0,
          cumulativeChargeAmount: 0,
          cumulativeDischargeAmount: 0,
          cumulativeTransferAmount: 0,
        }

        assertCumulativeValues(beforeResult, zeroValues)

        // STEP 2: Perform sync operations
        const syncOperations = [
          financialZoneAccountFuncs.syncMint({
            finAccount: financialZoneAccount,
            params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
          }),
          financialZoneAccountFuncs.syncBurn({
            finAccount: financialZoneAccount,
            params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
          }),
          financialZoneAccountFuncs.syncCharge({
            finAccount: financialZoneAccount,
            params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
          }),
          financialZoneAccountFuncs.syncDischarge({
            finAccount: financialZoneAccount,
            params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
          }),
          financialZoneAccountFuncs.syncTransfer({
            finAccount: financialZoneAccount,
            params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
          }),
        ]

        await Promise.all(syncOperations)

        // STEP 3: Check values after sync operations
        const afterResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        const updatedValues = {
          cumulativeAmount: 500,
          cumulativeMintAmount: 100,
          cumulativeBurnAmount: 100,
          cumulativeChargeAmount: 100,
          cumulativeDischargeAmount: 100,
          cumulativeTransferAmount: 100,
        }

        assertCumulativeValues(afterResult, updatedValues)

        // STEP 4: Reset cumulative values
        const params: CumulativeResetOption = {
          issuerId: BASE.ISSUER.ISSUER0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
        }

        await issuerFuncs.cumulativeReset({
          issuer: issuer,
          accounts: accounts,
          options: params,
        })

        // STEP 5: Check values after reset
        const finalResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        assertCumulativeValues(finalResult, zeroValues)
      })
    })
  })
})
