import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { time } from '@nomicfoundation/hardhat-network-helpers'
import { businessZoneAccountFuncs } from '@test/BusinessZoneAccount/helpers/function'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  BusinessZoneAccountInstance,
  ContractManagerInstance,
  FinancialZoneAccountInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'
import { contractManagerFuncs } from '@test/ContractManager/helpers/function'
import { financialZoneAccountFuncs } from '@test/FinancialZoneAccount/helpers/function'
import { FinancialZoneAccountContractType } from '@test/FinancialZoneAccount/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { tokenFuncs } from '@test/Token/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('checkCharge()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let financialZoneAccount: FinancialZoneAccountInstance
  let token: TokenInstance
  let contractManager: ContractManagerInstance
  let businessZoneAccount: BusinessZoneAccountInstance
  let accounts: SignerWithAddress[]

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, financialZoneAccount, token, contractManager, businessZoneAccount } =
        await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('account is not valid', () => {
      let ibcAddress
      let ibcAddressString
      before(async () => {
        ibcAddress = await accounts[0]
        ibcAddressString = await ibcAddress.getAddress()
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT2.ID },
        })
        await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: 500,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.BALANCE_SYNC,
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount: businessZoneAccount,
          accounts: accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT2.ID,
            zoneId: BASE.ZONE_ID.ID0,
            accountStatus: BASE.STATUS.TERMINATED,
          },
        })
      })

      it('should return false and error message when account is invalid', async () => {
        const result = await financialZoneAccount.checkCharge(BASE.ACCOUNT.EMPTY.ID, 100)
        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_INVALID_VAL, 'err')
      })

      it('should return false and error message when account is not registerd', async () => {
        const result = await financialZoneAccount.checkCharge(BASE.ACCOUNT.ACCOUNT0.ID, 100)
        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST, 'err')
      })
    })

    describe('exceed limit', () => {
      it('should return false and error when exceed limit', async () => {
        const param = {
          mint: 300,
          burn: 400,
          charge: 800,
          discharge: 100,
          transfer: 100,
          cumulative: {
            total: 500,
            mint: 100,
            burn: 100,
            charge: 100,
            discharge: 100,
            transfer: 100,
          },
        }
        const limitValues = {
          mint: param.mint,
          burn: param.burn,
          charge: param.charge,
          discharge: param.discharge,
          transfer: param.transfer,
          cumulative: {
            total: param.cumulative.total,
            mint: param.cumulative.mint,
            burn: param.cumulative.burn,
            charge: param.cumulative.charge,
            discharge: param.cumulative.discharge,
            transfer: param.cumulative.transfer,
          },
        }
        await financialZoneAccountFuncs.addAccountLimit({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, limitValues, BASE.TRACE_ID],
        })
        const result = await financialZoneAccount.checkCharge(BASE.ACCOUNT.ACCOUNT1.ID, 100)
        assert.equal(result.err, ERR.FINACCOUNT.EXCEEDED_DAILY_LIMIT)
      })
    })
  })
})

describe('checkCharge()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let financialZoneAccount: FinancialZoneAccountInstance
  let token: TokenInstance
  let contractManager: ContractManagerInstance
  let businessZoneAccount: BusinessZoneAccountInstance
  let accounts: SignerWithAddress[]

  describe('準正常系', () => {
    before(async () => {
      ;({ provider, issuer, validator, financialZoneAccount, token, contractManager, businessZoneAccount, accounts } =
        await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('account is not valid', () => {
      let ibcAddress
      let ibcAddressString
      before(async () => {
        ibcAddress = await accounts[0]
        ibcAddressString = await ibcAddress.getAddress()
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID0,
            zoneName: BASE.ZONE_NAME.NAME0,
          },
        })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT2.ID },
        })
        await tokenFuncs.mint({
          token: token,
          accounts: accounts,
          amount: 500,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.JPY_TOKEN_TRANSFER,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.ACCOUNT_SYNC,
        })
        await contractManagerFuncs.setIbcApp({
          contractManager: contractManager,
          accounts: accounts,
          ibcAddress: ibcAddressString,
          ibcAppName: BASE.IBCAPP_NAME.BALANCE_SYNC,
        })
        await businessZoneAccountFuncs.syncBusinessZoneStatus({
          businessZoneAccount: businessZoneAccount,
          accounts: accounts,
          options: {
            accountId: BASE.ACCOUNT.ACCOUNT2.ID,
            zoneId: BASE.ZONE_ID.ID0,
            accountStatus: BASE.STATUS.TERMINATED,
          },
        })
      })

      it('should return false and error message when account is invalid', async () => {
        const result = await financialZoneAccount.checkCharge(BASE.ACCOUNT.EMPTY.ID, 100)
        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_INVALID_VAL, 'err')
      })

      it('should return false and error message when account is not registerd', async () => {
        const result = await financialZoneAccount.checkCharge(BASE.ACCOUNT.ACCOUNT0.ID, 100)
        assert.equal(result.err, ERR.ACCOUNT.ACCOUNT_ID_NOT_EXIST, 'err')
      })
    })

    describe('exceed limit', () => {
      it('should return false and error when exceed limit', async () => {
        const param = {
          mint: 300,
          burn: 400,
          charge: 600,
          discharge: 100,
          transfer: 100,
          cumulative: {
            total: 500,
            mint: 100,
            burn: 100,
            charge: 120,
            discharge: 100,
            transfer: 100,
          },
        }
        const limitValues = {
          mint: param.mint,
          burn: param.burn,
          charge: param.charge,
          discharge: param.discharge,
          transfer: param.transfer,
          cumulative: {
            total: param.cumulative.total,
            mint: param.cumulative.mint,
            burn: param.cumulative.burn,
            charge: param.cumulative.charge,
            discharge: param.cumulative.discharge,
            transfer: param.cumulative.transfer,
          },
        }
        await financialZoneAccountFuncs.addAccountLimit({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, limitValues, BASE.TRACE_ID],
        })
        const result = await financialZoneAccount.checkCharge(BASE.ACCOUNT.ACCOUNT1.ID, 100)
        assert.equal(result.err, ERR.FINACCOUNT.EXCEEDED_DAILY_LIMIT)
      })

      it('should return error when exceed charge limit', async () => {
        const result = await financialZoneAccount.checkCharge(BASE.ACCOUNT.ACCOUNT1.ID, 601)
        assert.equal(result.err, ERR.FINACCOUNT.EXCEEDED_CHARGE_LIMIT)
      })

      it('should return error when exceed daily charge limit', async () => {
        const result = await financialZoneAccount.checkCharge(BASE.ACCOUNT.ACCOUNT1.ID, 200)
        assert.equal(result.err, ERR.FINACCOUNT.EXCEEDED_DAILY_CHARGE_LIMIT)
      })

      it('The system should return an error if a charge action exceeds the accumulated limit after the date changes', async () => {
        // 1日（24時間）時間を進める
        await time.increase(24 * 60 * 60)
        const result = await financialZoneAccount.checkCharge(BASE.ACCOUNT.ACCOUNT1.ID, 125)
        assert.equal(result.err, ERR.FINACCOUNT.EXCEEDED_DAILY_CHARGE_LIMIT)
      })

      it('If the date changes, the charge will be processed normally as long as the cumulative charge limit is not exceeded', async () => {
        // 1日（24時間）時間を進める
        await time.increase(24 * 60 * 60)
        const result = await financialZoneAccount.checkCharge(BASE.ACCOUNT.ACCOUNT1.ID, 10)
        assert.equal(result.success, true, 'success')
      })
    })
  })
})
