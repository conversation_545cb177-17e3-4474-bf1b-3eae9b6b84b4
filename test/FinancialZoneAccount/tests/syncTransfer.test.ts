import '@nomicfoundation/hardhat-chai-matchers'
import { anyValue } from '@nomicfoundation/hardhat-chai-matchers/withArgs'
import '@nomicfoundation/hardhat-ethers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { time } from '@nomicfoundation/hardhat-network-helpers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { FinancialZoneAccountInstance, IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { financialZoneAccountFuncs } from '@test/FinancialZoneAccount/helpers/function'
import { FinancialZoneAccountContractType } from '@test/FinancialZoneAccount/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('syncTransfer()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let financialZoneAccount: FinancialZoneAccountInstance
  let accounts: SignerWithAddress[]

  // TODO: TokenコントラクトにaddCumlativeAmount()関数実装後修正する
  const cumulativeAmount = 200

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, financialZoneAccount } =
        await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('cumulativeAmountが0の状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider: provider, accounts: accounts })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await validatorFuncs.addValidatorRole({ validator: validator, accounts: accounts })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })
      })

      it('日付が変わっていない場合、cumulativeAmountが加算されること', async () => {
        const jstDay = await utils.getJSTDay()
        const amount = 100

        const beforeResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        const tx = await financialZoneAccountFuncs.syncTransfer({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
        })

        const expectParams = {
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          amount: amount,
          cumulativeAmount: Number(beforeResult.accountData.cumulativeAmount) + amount,
          cumulativeTransferAmount:
            Number(beforeResult.accountData.cumulativeTransactionLimits.cumulativeTransferAmount) + amount,
          traceId: BASE.TRACE_ID,
        }
        await expect(tx)
          .to.emit(financialZoneAccount, 'SyncTransfer')
          .withArgs(
            expectParams.accountId,
            expectParams.amount,
            anyValue,
            expectParams.cumulativeAmount,
            expectParams.cumulativeTransferAmount,
            expectParams.traceId,
          )
        const afterResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        assert.equal(Number(afterResult.accountData.cumulativeDate), jstDay)
        assert.equal(
          Number(afterResult.accountData.cumulativeAmount),
          Number(beforeResult.accountData.cumulativeAmount) + amount,
        )
      })

      it('日付が変わっていない場合、cumulativeTransferAmountが加算されること', async () => {
        const jstDay = await utils.getJSTDay()
        const amount = 100

        const beforeResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        const tx = await financialZoneAccountFuncs.syncTransfer({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
        })

        const expectParams = {
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          amount: amount,
          cumulativeAmount: Number(beforeResult.accountData.cumulativeAmount) + amount,
          cumulativeTransferAmount:
            Number(beforeResult.accountData.cumulativeTransactionLimits.cumulativeTransferAmount) + amount,
          traceId: BASE.TRACE_ID,
        }
        await expect(tx)
          .to.emit(financialZoneAccount, 'SyncTransfer')
          .withArgs(
            expectParams.accountId,
            expectParams.amount,
            anyValue,
            expectParams.cumulativeAmount,
            expectParams.cumulativeTransferAmount,
            expectParams.traceId,
          )
        const afterResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        assert.equal(Number(afterResult.accountData.cumulativeDate), jstDay)
        assert.equal(
          Number(afterResult.accountData.cumulativeTransactionLimits.cumulativeTransferAmount),
          Number(beforeResult.accountData.cumulativeTransactionLimits.cumulativeTransferAmount) + amount,
        )
      })

      it('日跨りで一日の累積限度額を5回目の送金で超えている場合、cumulativeAmountがリセットされること', async () => {
        const amount = 1000
        await financialZoneAccountFuncs.syncTransfer({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
        })
        await financialZoneAccountFuncs.syncTransfer({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
        })
        await financialZoneAccountFuncs.syncTransfer({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
        })
        await financialZoneAccountFuncs.syncTransfer({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
        })
        const beforeResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        assert.equal(Number(beforeResult.accountData.cumulativeAmount), amount * 4 + 200)

        // 1日（24時間）時間を進める
        await time.increase(24 * 60 * 60)
        const mintTx = await financialZoneAccountFuncs.syncTransfer({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
        })

        const expectParams = {
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          amount: amount,
          cumulativeAmount: amount,
          cumulativeTransferAmount: amount,
          traceId: BASE.TRACE_ID,
        }
        await expect(mintTx)
          .to.emit(financialZoneAccount, 'SyncTransfer')
          .withArgs(
            expectParams.accountId,
            expectParams.amount,
            anyValue,
            expectParams.cumulativeAmount,
            expectParams.cumulativeTransferAmount,
            expectParams.traceId,
          )
        const afterResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        assert.equal(Number(afterResult.accountData.cumulativeAmount), amount)
      })

      it('日付が変った場合、cumulativeAmountがリセットされた上で加算されること', async () => {
        const amount = 100

        await time.increase(24 * 60 * 60)
        const jstDay = await utils.getJSTDay()

        const tx = await financialZoneAccountFuncs.syncTransfer({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
        })

        const expectParams = {
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          amount: amount,
          cumulativeAmount: amount,
          cumulativeTransferAmount: amount,
          traceId: BASE.TRACE_ID,
        }
        await expect(tx)
          .to.emit(financialZoneAccount, 'SyncTransfer')
          .withArgs(
            expectParams.accountId,
            expectParams.amount,
            anyValue,
            expectParams.cumulativeAmount,
            expectParams.cumulativeTransferAmount,
            expectParams.traceId,
          )
        const afterResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        assert.equal(Number(afterResult.accountData.cumulativeDate), jstDay)
        assert.equal(Number(afterResult.accountData.cumulativeAmount), amount)
      })

      it('日付が変った場合、cumulativeTransferAmountがリセットされた上で加算されること', async () => {
        const amount = 100

        await time.increase(24 * 60 * 60)
        const jstDay = await utils.getJSTDay()

        const tx = await financialZoneAccountFuncs.syncTransfer({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, amount, BASE.TRACE_ID],
        })

        const expectParams = {
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          amount: amount,
          cumulativeAmount: amount,
          cumulativeTransferAmount: amount,
          traceId: BASE.TRACE_ID,
        }
        await expect(tx)
          .to.emit(financialZoneAccount, 'SyncTransfer')
          .withArgs(
            expectParams.accountId,
            expectParams.amount,
            anyValue,
            expectParams.cumulativeAmount,
            expectParams.cumulativeTransferAmount,
            expectParams.traceId,
          )
        const afterResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        assert.equal(Number(afterResult.accountData.cumulativeDate), jstDay)
        assert.equal(Number(afterResult.accountData.cumulativeTransactionLimits.cumulativeTransferAmount), amount)
      })

      it('日付が変った場合、Transfer実行後にcumulativeTransferAmountが加算され、cumulativeMintAmountとcumulativeBurnAmountがリセットされること', async () => {
        // Move to the next day (add 24 hours)
        await time.increase(24 * 60 * 60)
        const mintAmount = 200
        const burnAmount = 50
        const transferAmount = 75

        // First day: Perform mint, burn and transfer operations
        await financialZoneAccountFuncs.syncMint({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, mintAmount, BASE.TRACE_ID],
        })

        await financialZoneAccountFuncs.syncBurn({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, burnAmount, BASE.TRACE_ID],
        })

        await financialZoneAccountFuncs.syncTransfer({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, transferAmount, BASE.TRACE_ID],
        })

        // Verify all amounts are set correctly after first day operations
        const firstDayResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        assert.equal(Number(firstDayResult.accountData.cumulativeTransactionLimits.cumulativeMintAmount), mintAmount)
        assert.equal(Number(firstDayResult.accountData.cumulativeTransactionLimits.cumulativeBurnAmount), burnAmount)
        assert.equal(
          Number(firstDayResult.accountData.cumulativeTransactionLimits.cumulativeTransferAmount),
          transferAmount,
        )

        // Move to the next day (add 24 hours)
        await time.increase(24 * 60 * 60)
        const jstDay = await utils.getJSTDay()

        // Second day: Perform only transfer operation
        const newTransferAmount = 100
        const tx = await financialZoneAccountFuncs.syncTransfer({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, newTransferAmount, BASE.TRACE_ID],
        })

        const expectParams = {
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          amount: newTransferAmount,
          cumulativeAmount: newTransferAmount,
          cumulativeTransferAmount: newTransferAmount,
          traceId: BASE.TRACE_ID,
        }

        await expect(tx)
          .to.emit(financialZoneAccount, 'SyncTransfer')
          .withArgs(
            expectParams.accountId,
            expectParams.amount,
            anyValue,
            expectParams.cumulativeAmount,
            expectParams.cumulativeTransferAmount,
            expectParams.traceId,
          )

        // Verify that after the day change and transfer operation:
        // 1. cumulativeTransferAmount is reset and set to the new transfer amount
        // 2. cumulativeMintAmount is reset to 0
        // 3. cumulativeBurnAmount is reset to 0
        const secondDayResult = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })

        assert.equal(Number(secondDayResult.accountData.cumulativeDate), jstDay)
        assert.equal(Number(secondDayResult.accountData.cumulativeAmount), newTransferAmount)
        assert.equal(
          Number(secondDayResult.accountData.cumulativeTransactionLimits.cumulativeTransferAmount),
          newTransferAmount,
        )
        assert.equal(Number(secondDayResult.accountData.cumulativeTransactionLimits.cumulativeMintAmount), 0)
        assert.equal(Number(secondDayResult.accountData.cumulativeTransactionLimits.cumulativeBurnAmount), 0)
      })
    })
  })
})
