import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { FinancialZoneAccountInstance, IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import { financialZoneAccountFuncs } from '@test/FinancialZoneAccount/helpers/function'
import { FinancialZoneAccountContractType } from '@test/FinancialZoneAccount/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { before } from 'mocha'

// Chai global vars
declare let assert: Chai.Assert

describe('addAccountLimit()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let financialZoneAccount: FinancialZoneAccountInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, financialZoneAccount } =
        await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('provider, providerRole, issuer, validator, account, tokenが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider: provider, accounts: accounts })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await validatorFuncs.addValidatorRole({ validator: validator, accounts: accounts })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })
      })
      it('アカウント限度額追加できること', async () => {
        const param = {
          mint: 1300,
          burn: 1400,
          charge: 1200,
          discharge: 1600,
          transfer: 1100,
          cumulative: {
            total: 5000,
            mint: 1000,
            burn: 1000,
            charge: 1000,
            discharge: 1000,
            transfer: 1000,
          },
        }
        const limitValues = {
          mint: param.mint,
          burn: param.burn,
          charge: param.charge,
          discharge: param.discharge,
          transfer: param.transfer,
          cumulative: {
            total: param.cumulative.total,
            mint: param.cumulative.mint,
            burn: param.cumulative.burn,
            charge: param.cumulative.charge,
            discharge: param.cumulative.discharge,
            transfer: param.cumulative.transfer,
          },
        }
        await financialZoneAccountFuncs.addAccountLimit({
          finAccount: financialZoneAccount,
          params: [BASE.ACCOUNT.ACCOUNT1.ID, limitValues, BASE.TRACE_ID],
        })
        const result = await validatorFuncs.getAccount({
          validator: validator,
          prams: [BASE.VALID.VALID0.ID, BASE.ACCOUNT.ACCOUNT1.ID],
        })
        assert.equal(Number(result.accountData.transferLimit), param.transfer)
        assert.equal(Number(result.accountData.chargeLimit), param.charge)
        assert.equal(Number(result.accountData.mintLimit), param.mint)
        assert.equal(Number(result.accountData.burnLimit), param.burn)
        assert.equal(Number(result.accountData.cumulativeLimit), param.cumulative.total)
      })
    })
  })
  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, financialZoneAccount } =
        await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('共通領域Accountが登録されていない状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider: provider, accounts: accounts })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await validatorFuncs.addValidatorRole({ validator: validator, accounts: accounts })
      })
    })
  })
})
