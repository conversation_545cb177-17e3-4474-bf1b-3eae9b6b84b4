import '@nomicfoundation/hardhat-chai-matchers'
import '@nomicfoundation/hardhat-ethers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import {
  FinancialZoneAccountInstance,
  IssuerInstance,
  ModTokenLimitOption,
  ProviderInstance,
  ValidatorInstance,
} from '@test/common/types'
import { FinancialZoneAccountContractType } from '@test/FinancialZoneAccount/helpers/types'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'
import { expect } from 'chai'
import { before } from 'mocha'

describe('modAccountLimit', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let accounts: SignerWithAddress[]

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator } = await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('limitUpdates is false', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({ validator: validator, accounts: accounts })
      })

      it('no data change when limitUpdates is false', async () => {
        const params: ModTokenLimitOption = {
          issuerId: BASE.ISSUER.ISSUER0.ID,
          accountId: BASE.ACCOUNT.ACCOUNT1.ID,
          limitUpdates: {
            mint: false,
            burn: false,
            charge: false,
            discharge: false,
            transfer: false,
            cumulative: {
              total: false,
              mint: false,
              burn: false,
              charge: false,
              discharge: false,
              transfer: false,
            },
          },
          limitValues: {
            mint: 10000,
            burn: 100,
            charge: 100,
            discharge: 100,
            transfer: 100,
            cumulative: {
              total: 10,
              mint: 10,
              burn: 10,
              charge: 10,
              discharge: 10,
              transfer: 10,
            },
          },
        }

        const { issuerId, ...expected } = params

        const tx = await issuerFuncs.modTokenLimit({ issuer: issuer, accounts: accounts, options: params })

        await expect(tx)
          .to.emit(issuer, 'ModTokenLimit')
          .withArgs(
            BASE.VALID.VALID0.ID, // validatorId
            expected.accountId, // accountId
            [
              false, // mint
              false, // burn
              false, // charge
              false, // discharge
              false, // transfer
              [
                false, // total
                false, // mint
                false, // burn
                false, // charge
                false, // discharge
                false, // transfer
              ],
            ],
            [
              BASE.LIMIT_VALUES.mint,
              BASE.LIMIT_VALUES.burn,
              BASE.LIMIT_VALUES.charge,
              BASE.LIMIT_VALUES.discharge,
              BASE.LIMIT_VALUES.transfer,
              [
                BASE.LIMIT_VALUES.cumulative.total,
                BASE.LIMIT_VALUES.cumulative.mint,
                BASE.LIMIT_VALUES.cumulative.burn,
                BASE.LIMIT_VALUES.cumulative.charge,
                BASE.LIMIT_VALUES.cumulative.discharge,
                BASE.LIMIT_VALUES.cumulative.transfer,
              ],
            ],
            BASE.TRACE_ID, // traceId
          )
      })
    })
  })
})

describe('modAccountLimit()', () => {
  let financialZoneAccount: FinancialZoneAccountInstance

  describe('準正常系', () => {
    before(async () => {
      ;({ financialZoneAccount } = await contractFixture<FinancialZoneAccountContractType>())
    })

    describe('caller is not valid', () => {
      it('should revert when caller is not issuer contract', async () => {
        const limitUpdates = {
          mint: true,
          burn: true,
          charge: true,
          discharge: true,
          transfer: true,
          cumulative: {
            total: true,
            mint: true,
            burn: true,
            charge: true,
            discharge: true,
            transfer: true,
          },
        }
        const limitValues = {
          mint: 10000,
          burn: 100,
          charge: 100,
          discharge: 100,
          transfer: 100,
          cumulative: {
            total: 10,
            mint: 10,
            burn: 10,
            charge: 10,
            discharge: 10,
            transfer: 10,
          },
        }
        await expect(
          financialZoneAccount.modAccountLimit(BASE.ACCOUNT.ACCOUNT0.ID, limitUpdates, limitValues),
        ).to.be.revertedWith(ERR.ISSUER.NOT_ISSUER_CONTRACT)
      })
    })
  })
})
