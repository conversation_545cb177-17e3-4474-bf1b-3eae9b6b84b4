import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { accountFuncs } from '@test/Account/helpers/function'
import { AccountContractType } from '@test/Account/helpers/types'
import { BASE, ERR } from '@test/common/consts'
import { contractFixture } from '@test/common/contractFixture'
import { AccountInstance, IssuerInstance, ProviderInstance, ValidatorInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'
import { providerFuncs } from '@test/Provider/helpers/function'
import { validatorFuncs } from '@test/Validator/helpers/function'

describe('getAccountId()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let validator: ValidatorInstance
  let account: AccountInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ accounts, provider, issuer, validator, account } = await contractFixture<AccountContractType>())
    })

    describe('accountが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({
          provider: provider,
          accounts: accounts,
          options: { zoneId: BASE.ZONE_ID.ID1 },
        })
        await providerFuncs.addProviderRole({
          provider: provider,
          accounts: accounts,
          options: {
            providerEoa: await accounts[BASE.EOA.PROV2].getAddress(),
          },
        })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await validatorFuncs.addValidator({ validator: validator, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts, options: { eoaKey: BASE.EOA.PROV2 } })
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT0.ID },
        })
        await validatorFuncs.addAccount({
          validator: validator,
          accounts: accounts,
          options: { accountId: BASE.ACCOUNT.ACCOUNT1.ID },
        })
      })

      it('0を指定すると1番目のアカウントが取得する', async () => {
        const result = await accountFuncs.getAccountId({ account: account, params: [0] })
        utils.assertEqualForEachField(result, { accountId: BASE.ACCOUNT.ACCOUNT0.ID, err: '' })
      })

      it('範囲外インデックスが指定する場合エラー', async () => {
        const result = await accountFuncs.getAccountId({ account: account, params: [10] })
        utils.assertEqualForEachField(result, { err: ERR.ACCOUNT.ACCOUNT_OUT_OF_INDEX })
      })

      it('2番目のアカウントが取得する', async () => {
        const result = await accountFuncs.getAccountId({ account: account, params: [1] })
        utils.assertEqualForEachField(result, { accountId: BASE.ACCOUNT.ACCOUNT1.ID, err: '' })
      })
    })
  })
})
