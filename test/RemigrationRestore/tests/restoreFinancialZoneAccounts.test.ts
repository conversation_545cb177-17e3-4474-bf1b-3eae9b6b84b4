import { BASE, ERR } from '@test/common/consts'
import { RemigrationBackupInstance, RemigrationRestoreInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { toBytes32 } from '@test/common/utils'
import { remigrationFuncs } from '@test/RemigrationRestore/helpers/function'
import { RemigrationRestoreContractType } from '@test/RemigrationRestore/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'
import { PromiseType } from 'utility-types'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'

// Chai global vars
declare let assert: Chai.Assert

describe('restoreFinancialZoneAccounts()', () => {
  let accounts: SignerWithAddress[]
  let remigrationBackup: RemigrationBackupInstance
  let remigrationRestore: RemigrationRestoreInstance

  let createFinAccountParams
  let createAccountParams
  describe('正常系', () => {
    before(async () => {
      createFinAccountParams = async (accounts: any, num: number) => {
        const createAccParams = Promise.all(
          [...Array(num).keys()].map((index) => {
            return {
              accountId: toBytes32(`x${index}`),
              financialZoneAccountData: {
                mintLimit: BASE.LIMIT_AMOUNTS[0],
                burnLimit: BASE.LIMIT_AMOUNTS[1],
                chargeLimit: BASE.LIMIT_AMOUNTS[2],
                dischargeLimit: 0,
                transferLimit: BASE.LIMIT_AMOUNTS[3],
                cumulativeLimit: BASE.LIMIT_AMOUNTS[4],
                cumulativeAmount: 0,
                cumulativeDate: 0,
                cumulativeTransactionLimits: {
                  cumulativeMintLimit: 0,
                  cumulativeMintAmount: 0,
                  cumulativeBurnLimit: 0,
                  cumulativeBurnAmount: 0,
                  cumulativeChargeLimit: 0,
                  cumulativeChargeAmount: 0,
                  cumulativeDischargeLimit: 0,
                  cumulativeDischargeAmount: 0,
                  cumulativeTransferLimit: 0,
                  cumulativeTransferAmount: 0,
                },
              },
            }
          }),
        )
        return createAccParams
      }

      createAccountParams = async (accounts: any, num: number) => {
        return Promise.all(
          [...Array(num).keys()].map((index) => {
            return {
              accountId: toBytes32(`x${index}`),
              accountName: toBytes32(`NAME${index}`),
              accountStatus: BASE.STATUS.ACTIVE,
              zoneIds: ['3000', '4000'],
              balance: 100,
              reasonCode: BASE.REASON_CODE2,
              appliedAt: ********,
              registeredAt: ********,
              terminatingAt: ********,
              terminatedAt: ********,
              validatorId: BASE.VALID.VALID0.ID,
              accountIdExistence: true,
              accountEoa: accounts[index],
              accountApprovalAll: [
                {
                  spanderId: toBytes32(`SPANDER${index}_ID1`),
                  spenderAccountName: toBytes32(`SPENDER${index}_NAME1`),
                  allowanceAmount: 300,
                  approvedAt: ********,
                },
                {
                  spanderId: toBytes32(`SPANDER${index}_ID2`),
                  spenderAccountName: toBytes32(`SPANDER${index}_NAME2`),
                  allowanceAmount: 400,
                  approvedAt: ********,
                },
              ],
            }
          }),
        )
      }
      ;({ accounts, remigrationBackup, remigrationRestore } = await contractFixture<RemigrationRestoreContractType>())
    })

    describe('初期状態', () => {
      let finAccountParam
      let accountParam
      const assertList = (
        result: PromiseType<ReturnType<typeof remigrationFuncs.backupFinancialZoneAccounts>>,
        expected: typeof finAccountParam,
      ) => {
        assert.strictEqual(result.financialZoneAccounts.length, expected.length, 'accounts count')
        expected.forEach((v, i) => {
          const account = result.financialZoneAccounts[i]
          const financialZoneAccountData = account.financialZoneAccountData
          const cumulativeTransactionLimits = financialZoneAccountData.cumulativeTransactionLimits
          assert.equal(account.accountId, v.accountId)
          assert.equal(financialZoneAccountData.mintLimit, BASE.LIMIT_AMOUNTS[0])
          assert.equal(financialZoneAccountData.burnLimit, BASE.LIMIT_AMOUNTS[1])
          assert.equal(financialZoneAccountData.chargeLimit, BASE.LIMIT_AMOUNTS[2])
          assert.equal(financialZoneAccountData.dischargeLimit, 0)
          assert.equal(financialZoneAccountData.transferLimit, BASE.LIMIT_AMOUNTS[3])
          assert.equal(financialZoneAccountData.cumulativeLimit, BASE.LIMIT_AMOUNTS[4])
          assert.equal(financialZoneAccountData.cumulativeAmount, 0)
          assert.equal(financialZoneAccountData.cumulativeDate, 0)
          assert.equal(cumulativeTransactionLimits.cumulativeMintLimit, 0)
          assert.equal(cumulativeTransactionLimits.cumulativeMintAmount, 0)
          assert.equal(cumulativeTransactionLimits.cumulativeBurnLimit, 0)
          assert.equal(cumulativeTransactionLimits.cumulativeBurnAmount, 0)
          assert.equal(cumulativeTransactionLimits.cumulativeChargeLimit, 0)
          assert.equal(cumulativeTransactionLimits.cumulativeChargeAmount, 0)
          assert.equal(cumulativeTransactionLimits.cumulativeDischargeLimit, 0)
          assert.equal(cumulativeTransactionLimits.cumulativeDischargeAmount, 0)
          assert.equal(cumulativeTransactionLimits.cumulativeTransferLimit, 0)
          assert.equal(cumulativeTransactionLimits.cumulativeTransferAmount, 0)
        })
      }

      before(async () => {
        finAccountParam = await createFinAccountParams(accounts, 20)
        accountParam = await createAccountParams(accounts, 20)
      })

      it('全てのaccounts(20件)が登録できること', async () => {
        await remigrationFuncs.restoreAccounts({
          remigration: remigrationRestore,
          accs: accountParam,
          accounts: accounts,
        })
        await remigrationFuncs.restoreFinancialZoneAccounts({
          remigration: remigrationRestore,
          finAccounts: finAccountParam,
          accounts: accounts,
        })
        const result = await remigrationFuncs.backupFinancialZoneAccounts({
          remigration: remigrationBackup,
          offset: 0,
          limit: 1000,
        })
        assertList(result, finAccountParam)
      })
    })
  })

  describe('準正常系', () => {
    let accountsPram

    before(async () => {
      ;({ accounts, remigrationBackup, remigrationRestore } = await contractFixture<RemigrationRestoreContractType>())
      accountsPram = await createFinAccountParams(accounts, 20)
    })

    describe('初期状態', () => {
      it('Admin権限がない場合、エラーがスローされること', async () => {
        await expect(
          remigrationFuncs.restoreFinancialZoneAccounts({
            remigration: remigrationRestore,
            finAccounts: accountsPram,
            accounts: accounts,
            options: {
              eoaKey: BASE.EOA.ISSUER1,
            },
          }),
          ERR.ACCOUNT.ACCOUNT_NOT_ADMIN,
        )
      })

      it('署名無効の場合、エラーがスローされること', async () => {
        await expect(
          remigrationFuncs.restoreFinancialZoneAccounts({
            remigration: remigrationRestore,
            finAccounts: accountsPram,
            accounts: accounts,
            options: {
              sig: ['0x1234', ''],
            },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const now = await utils.getExceededDeadline()
        await expect(
          remigrationFuncs.restoreFinancialZoneAccounts({
            remigration: remigrationRestore,
            finAccounts: accountsPram,
            accounts: accounts,
            options: {
              deadline: now,
            },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('異常な値が入力された時にfails', async () => {
        const accountsIvalid = [
          {
            accountId: '123',
            accountName: 'name',
            accountStatus: '123',
            zoneIds: [],
            balance: '456',
            reasonCode: true,
            appliedAt: ********,
            registeredAt: ********,
            terminatingAt: ********,
            terminatedAt: ********,
            accountIdExistence: true,
            accountEoa: '',
            accountApprovalAll: [],
          },
        ]
        await expect(
          remigrationFuncs.restoreFinancialZoneAccounts({
            remigration: remigrationRestore,
            finAccounts: accountsIvalid,
            accounts: accounts,
          }),
        ).to.be.throw
      })
    })
  })
})
