import { BASE, ERR } from '@test/common/consts'
import { RemigrationBackupInstance, RemigrationRestoreInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { toBytes32 } from '@test/common/utils'
import { remigrationFuncs } from '@test/RemigrationRestore/helpers/function'
import { RemigrationRestoreContractType } from '@test/RemigrationRestore/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'
import { PromiseType } from 'utility-types'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'

// Chai global vars
declare let assert: Chai.Assert

describe('restoreIssuers()', () => {
  let accounts: SignerWithAddress[]
  let remigrationBackup: RemigrationBackupInstance
  let remigrationRestore: RemigrationRestoreInstance

  let createParams
  describe('正常系', () => {
    before(async () => {
      createParams = (accounts: any, num: number) => {
        const createAccParams = Promise.all(
          [...Array(num).keys()].map((index) => {
            return {
              issuerId: toBytes32(`x${index}`),
              role: toBytes32(`ROLE${index}`),
              name: toBytes32(`NAME${index}`),
              bankCode: index,
              issuerIdExistence: true,
              issuerEoa: accounts[index],
              issuerAccountExistence: [
                {
                  accountId: toBytes32(`ISSUER${index}_ACCOUNT1`),
                  accountIdExistenceByIssuerId: true,
                },
                {
                  accountId: toBytes32(`ISSUER${index}_ACCOUNT2`),
                  accountIdExistenceByIssuerId: true,
                },
              ],
            }
          }),
        )
        return createAccParams
      }
      ;({ accounts, remigrationBackup, remigrationRestore } = await contractFixture<RemigrationRestoreContractType>())
    })

    describe('初期状態', () => {
      let issuersPram
      const assertList = (
        result: PromiseType<ReturnType<typeof remigrationFuncs.backupIssuers>>,
        expected: typeof issuersPram,
      ) => {
        assert.strictEqual(result.issuers.length, expected.length, 'issuers count')
        expected.forEach((v, i) => {
          assert.isString(result.issuers[i].role, 'role')
          utils.assertEqualForEachField(result.issuers[i], {
            issuerId: v.issuerId,
            role: v.role,
            name: v.name,
            bankCode: v.bankCode.toString(),
            issuerIdExistence: v.issuerIdExistence,
            issuerEoa: v.issuerEoa,
          })
          v.issuerAccountExistence.forEach((v2, i2) => {
            utils.assertEqualForEachField(result.issuers[i].issuerAccountExistence[i2], {
              accountId: v2.accountId,
              accountIdExistenceByIssuerId: v2.accountIdExistenceByIssuerId,
            })
          })
        })
      }

      before(async () => {
        issuersPram = await createParams(accounts, 20)
      })

      it('全てのissuers(20件)が登録できること', async () => {
        await remigrationFuncs.restoreIssuers({
          remigration: remigrationRestore,
          issuers: issuersPram,
          accounts: accounts,
        })
        const result = await remigrationFuncs.backupIssuers({
          remigration: remigrationBackup,
          offset: 0,
          limit: issuersPram.length,
        })
        assertList(result, issuersPram)
      })
    })
  })

  describe('準正常系', () => {
    let issuersPram

    before(async () => {
      ;({ accounts, remigrationBackup, remigrationRestore } = await contractFixture<RemigrationRestoreContractType>())
      issuersPram = await createParams(accounts, 20)
    })

    describe('初期状態', () => {
      it('Admin権限がない場合、エラーがスローされること', async () => {
        await expect(
          remigrationFuncs.restoreIssuers({
            remigration: remigrationRestore,
            issuers: issuersPram,
            accounts: accounts,
            options: { eoaKey: BASE.EOA.ISSUER1 },
          }),
        ).to.be.revertedWith(ERR.ISSUER.ISSUER_NOT_ADMIN_ROLE)
      })

      it('署名無効の場合、エラーがスローされること', async () => {
        await expect(
          remigrationFuncs.restoreIssuers({
            remigration: remigrationRestore,
            issuers: issuersPram,
            accounts: accounts,
            options: { sig: ['0x1234', ''] },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const now = await utils.getExceededDeadline()
        await expect(
          remigrationFuncs.restoreIssuers({
            remigration: remigrationRestore,
            issuers: issuersPram,
            accounts: accounts,
            options: { deadline: now },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('異常な値が入力された時にfails', async () => {
        const issuerInvalid = [
          {
            validatorId: '123',
            name: 'name',
            issuerId: '123',
            role: '456',
            validatorAccountId: '123',
            enabled: true,
            validatorIdExistence: true,
            issuerIdLinkedFlag: true,
            issuerEoa: '',
            validAccountExistence: [],
          },
        ]
        await expect(
          remigrationFuncs.restoreIssuers({
            remigration: remigrationRestore,
            issuers: issuerInvalid,
            accounts: accounts,
          }),
        ).to.be.throw
      })
    })
  })
})
