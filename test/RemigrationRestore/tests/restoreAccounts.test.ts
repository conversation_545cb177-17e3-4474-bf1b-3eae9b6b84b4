import { BASE, ERR } from '@test/common/consts'
import { RemigrationBackupInstance, RemigrationRestoreInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { toBytes32 } from '@test/common/utils'
import { remigrationFuncs } from '@test/RemigrationRestore/helpers/function'
import { RemigrationRestoreContractType } from '@test/RemigrationRestore/helpers/types'
import { expect } from 'chai'
import { before } from 'mocha'
import { PromiseType } from 'utility-types'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'

// Chai global vars
declare let assert: Chai.Assert

describe('restoreAccounts()', () => {
  let accounts: SignerWithAddress[]
  let remigrationBackup: RemigrationBackupInstance
  let remigrationRestore: RemigrationRestoreInstance

  let createParams
  describe('正常系', () => {
    before(async () => {
      createParams = async (accounts: any, num: number) => {
        const createAccParams = Promise.all(
          [...Array(num).keys()].map((index) => {
            return {
              accountId: toBytes32(`x${index}`),
              accountName: toBytes32(`NAME${index}`),
              accountStatus: BASE.STATUS.ACTIVE,
              zoneIds: ['3000', '4000'],
              balance: 100,
              reasonCode: BASE.REASON_CODE2,
              appliedAt: ********,
              registeredAt: ********,
              terminatingAt: ********,
              terminatedAt: ********,
              validatorId: BASE.VALID.VALID0.ID,
              accountIdExistence: true,
              accountEoa: accounts[index],
              accountApprovalAll: [
                {
                  spanderId: toBytes32(`SPANDER${index}_ID1`),
                  spenderAccountName: toBytes32(`SPENDER${index}_NAME1`),
                  allowanceAmount: 300,
                  approvedAt: ********,
                },
                {
                  spanderId: toBytes32(`SPANDER${index}_ID2`),
                  spenderAccountName: toBytes32(`SPANDER${index}_NAME2`),
                  allowanceAmount: 400,
                  approvedAt: ********,
                },
              ],
            }
          }),
        )
        return createAccParams
      }
      ;({ accounts, remigrationBackup, remigrationRestore } = await contractFixture<RemigrationRestoreContractType>())
    })

    describe('初期状態', () => {
      let accountsPram
      const assertList = (
        result: PromiseType<ReturnType<typeof remigrationFuncs.backupAccounts>>,
        expected: typeof accountsPram,
      ) => {
        assert.strictEqual(result.accounts.length, expected.length, 'accounts count')
        expected.forEach((v, i) => {
          utils.assertEqualForEachField(result.accounts[i], {
            accountId: v.accountId,
            accountName: v.accountName,
            accountStatus: v.accountStatus,
            balance: String(v.balance),
            reasonCode: String(v.reasonCode),
            appliedAt: v.appliedAt,
            registeredAt: v.registeredAt,
            terminatingAt: v.terminatingAt,
            terminatedAt: v.terminatedAt,
            validatorId: v.validatorId,
            accountIdExistence: v.accountIdExistence,
            accountEoa: v.accountEoa,
          })
          v.zoneIds.forEach((v2, i2) => {
            assert.strictEqual(result.accounts[i].zoneIds[i2], v2)
          })
          v.accountApprovalAll.forEach((v2, i2) => {
            utils.assertEqualForEachField(result.accounts[i].accountApprovalAll[i2], {
              spanderId: v2.spanderId,
              spenderAccountName: v2.spenderAccountName,
              allowanceAmount: String(v2.allowanceAmount),
              approvedAt: v2.approvedAt,
            })
          })
        })
      }

      before(async () => {
        accountsPram = await createParams(accounts, 20)
      })
      it('全てのaccounts(20件)が登録できること', async () => {
        await remigrationFuncs.restoreAccounts({
          remigration: remigrationRestore,
          accs: accountsPram,
          accounts: accounts,
        })
        const result = await remigrationFuncs.backupAccounts({ remigration: remigrationBackup, offset: 0, limit: 1000 })
        assertList(result, accountsPram)
      })
    })
  })

  describe('準正常系', () => {
    let accountsPram

    before(async () => {
      ;({ accounts, remigrationBackup, remigrationRestore } = await contractFixture<RemigrationRestoreContractType>())
      accountsPram = await createParams(accounts, 20)
    })

    describe('初期状態', () => {
      it('Admin権限がない場合、エラーがスローされること', async () => {
        await expect(
          remigrationFuncs.restoreAccounts({
            remigration: remigrationRestore,
            accs: accountsPram,
            accounts: accounts,
            options: { eoaKey: BASE.EOA.ISSUER1 },
          }),
        ).to.be.revertedWith(ERR.ACCOUNT.ACCOUNT_NOT_ADMIN)
      })

      it('署名無効の場合、エラーがスローされること', async () => {
        await expect(
          remigrationFuncs.restoreAccounts({
            remigration: remigrationRestore,
            accs: accountsPram,
            accounts: accounts,
            options: { sig: ['0x1234', ''] },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const now = await utils.getExceededDeadline()
        await expect(
          remigrationFuncs.restoreAccounts({
            remigration: remigrationRestore,
            accs: accountsPram,
            accounts: accounts,
            options: { deadline: now },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('異常な値が入力された時にfails', async () => {
        const accountsIvalid = [
          {
            accountId: '123',
            accountName: 'name',
            accountStatus: '123',
            zoneIds: [],
            balance: '456',
            reasonCode: true,
            appliedAt: ********,
            registeredAt: ********,
            terminatingAt: ********,
            terminatedAt: ********,
            accountIdExistence: true,
            accountEoa: '',
            accountApprovalAll: [],
          },
        ]
        await expect(
          remigrationFuncs.restoreAccounts({
            remigration: remigrationRestore,
            accs: accountsIvalid,
            accounts: accounts,
          }),
        ).to.be.throw
      })
    })
  })
})
