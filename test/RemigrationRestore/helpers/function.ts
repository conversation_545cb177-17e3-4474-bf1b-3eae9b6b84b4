import { BASE } from '@test/common/consts'
import { EventReturnType } from '@test/common/types'
import * as utils from '@test/common/utils'
import {
  BackupAccountsType,
  BackupBusinessZoneAccountsType,
  BackupFinancialZoneAccountsType,
  BackupIssuersType,
  BackupProviderType,
  BackupTokenType,
  BackupValidatorsType,
  RemigrationRestoreType,
  RestoreAccountsType,
  RestoreBusinessZoneAccountsType,
  RestoreFinancialZoneAccountsType,
  RestoreIssuersType,
  RestoreProvidersType,
  RestoreTokenType,
  RestoreValidatorsType,
} from './types'
import privateKey from '@/privateKey'

export const remigrationFuncs = {
  version: ({ remigration }: RemigrationRestoreType) => {
    return remigration.version()
  },
  backupValidators: async ({ remigration, offset, limit, options = {} }: BackupValidatorsType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.GET_VALIDATORS_ALL } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return remigration.backupValidators(offset, limit, _deadline, _sig[0]) as unknown as Promise<
      EventReturnType['Remigration']['BackupValidators']
    >
  },
  restoreValidators: async ({ remigration, validators, accounts, options = {} }: RestoreValidatorsType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.SET_VALIDATORS_ALL } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return remigration.connect(accounts[9]).restoreValidators(validators, _deadline, _sig[0])
  },
  backupProvider: async ({ remigration, options = {} }: BackupProviderType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.GET_PROVIDER_ALL } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return remigration.backupProviders(_deadline, _sig[0]) as unknown as Promise<
      EventReturnType['Remigration']['BackupProvider']
    >
  },
  backupAccounts: async ({ remigration, offset, limit, options = {} }: BackupAccountsType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.GET_ACCOUNTS_ALL } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return remigration.backupAccounts(offset, limit, _deadline, _sig[0]) as unknown as Promise<
      EventReturnType['Remigration']['BackupAccounts']
    >
  },
  backupFinancialZoneAccounts: async ({
    remigration,
    offset,
    limit,
    options = {},
  }: BackupFinancialZoneAccountsType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.GET_FINACCOUNTS_ALL } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return remigration.backupFinancialZoneAccounts(offset, limit, _deadline, _sig[0]) as unknown as Promise<
      EventReturnType['Remigration']['BackupFinAccounts']
    >
  },
  backupIssuers: async ({ remigration, offset, limit, options = {} }: BackupIssuersType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.GET_ISSUERS_ALL } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return remigration.backupIssuers(offset, limit, _deadline, _sig[0]) as unknown as Promise<
      EventReturnType['Remigration']['BackupIssuers']
    >
  },
  restoreProviders: async ({ remigration, providers, accounts, options = {} }: RestoreProvidersType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.SET_PROVIDER_ALL } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return remigration.connect(accounts[9]).restoreProviders(providers, _deadline, _sig[0])
  },
  restoreAccounts: async ({ remigration, accs, accounts, options = {} }: RestoreAccountsType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.SET_ACCOUNTS_ALL } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return remigration.connect(accounts[9]).restoreAccounts(accs, _deadline, _sig[0])
  },
  restoreFinancialZoneAccounts: async ({
    remigration,
    finAccounts,
    accounts,
    options = {},
  }: RestoreFinancialZoneAccountsType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.SET_FINACCOUNTS_ALL } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return remigration.connect(accounts[9]).restoreFinancialZoneAccounts(finAccounts, _deadline, _sig[0])
  },
  restoreIssuers: async ({ remigration, issuers, accounts, options = {} }: RestoreIssuersType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.SET_ISSUERS_ALL } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return remigration.connect(accounts[9]).restoreIssuers(issuers, _deadline, _sig[0])
  },
  backupToken: async ({ remigration, options = {} }: BackupTokenType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.GET_TOKEN_ALL } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return remigration.backupToken(_deadline, _sig[0]) as unknown as Promise<
      EventReturnType['Remigration']['BackupToken']
    >
  },
  restoreToken: async ({ remigration, token, accounts, options = {} }: RestoreTokenType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.SET_TOKEN_ALL } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return remigration.connect(accounts[9]).restoreToken(token, _deadline, _sig[0])
  },
  backupBusinessZoneAccounts: async ({ remigration, offset, limit, options = {} }: BackupBusinessZoneAccountsType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.GET_BIZACCOUNTS_ALL } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])

    return remigration.backupBusinessZoneAccounts(offset, limit, _deadline, _sig[0]) as unknown as Promise<
      EventReturnType['Remigration']['BackupBusinessZoneAccounts']
    >
  },
  restoreBusinessZoneAccounts: async ({
    remigration,
    bizAccounts,
    accounts,
    options = {},
  }: RestoreBusinessZoneAccountsType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash = BASE.SALTS.SET_BIZACCOUNTS_ALL } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [hash, _deadline])
    return remigration.connect(accounts[9]).restoreBusinessZoneAccounts(bizAccounts, _deadline, _sig[0])
  },
}
