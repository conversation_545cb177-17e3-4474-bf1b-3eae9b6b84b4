import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccessCtrlMockInstance,
  AccountSyncBridgeInstance,
  BusinessZoneAccountMockInstance,
  ContractCallOption,
  IBCHandlerInstance,
  IBCTokenMockInstance,
  PacketCallOption,
  ProviderMockInstance,
  SyncAccountCallOption,
  ValidatorMockInstance,
} from '@test/common/types'
import { ContractTransactionResponse } from 'ethers'

export type AccountSyncBridgeContractType = {
  accounts: SignerWithAddress[]
  accountSyncBridge: AccountSyncBridgeInstance
  ibcHandler: IBCHandlerInstance
  providerMock: ProviderMockInstance
  validatorMock: ValidatorMockInstance
  accessCtrlMock: AccessCtrlMockInstance
  businessZoneAccountMock: BusinessZoneAccountMockInstance
  ibcTokenMock: IBCTokenMockInstance
}

type AccountSyncBridgeType = {
  accountSyncBridge: AccountSyncBridgeInstance
}

type PacketType = AccountSyncBridgeType & {
  ibcHandler: IBCHandlerInstance
  options?: Partial<ContractCallOption & PacketCallOption>
}

export type FuncParamsType = {
  setAddress: AccountSyncBridgeType & {
    providerMockAddress: string
    validatorMockAddress: string
    accessCtrlMockAddress: string
    businessZoneAccountMockAddress: string
    ibcTokenMockAddress: string
    options?: Partial<ContractCallOption & { newAddress: string }>
  }
  syncAccount: AccountSyncBridgeType & {
    options?: Partial<ContractCallOption & SyncAccountCallOption>
  }
  recvPacket: PacketType
  acknowledgementPacket: PacketType
  timeoutPacket: PacketType
  recoverPacket: AccountSyncBridgeType & {
    accountSyncBridge: AccountSyncBridgeInstance
    options?: Partial<ContractCallOption & PacketCallOption>
  }
}
export type FunctionType = {
  setAddress: (args: FuncParamsType['setAddress']) => Promise<ContractTransactionResponse>
  syncAccount: (args: FuncParamsType['syncAccount']) => Promise<ContractTransactionResponse>
  recvPacket: (args: FuncParamsType['recvPacket']) => Promise<ContractTransactionResponse>
  acknowledgementPacket: (args: FuncParamsType['acknowledgementPacket']) => Promise<ContractTransactionResponse>
  timeoutPacket: (args: FuncParamsType['timeoutPacket']) => Promise<ContractTransactionResponse>
  recoverPacket: (args: FuncParamsType['recoverPacket']) => Promise<ContractTransactionResponse>
}
