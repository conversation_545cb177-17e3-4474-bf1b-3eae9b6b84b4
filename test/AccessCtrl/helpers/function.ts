import { BASE } from '@test/common/consts'
import * as utils from '@test/common/utils'
import { FunctionType } from './types'
import privateKey from '@/privateKey'

/**
 * accessCtrlのイベントを呼ぶ関数を持つobject
 */
export const accessCtrlFuncs: FunctionType = {
  version: ({ accessCtrl }) => {
    return accessCtrl.version()
  },
  calcRole: async (args) => {
    return await args.accessCtrl.calcRole(args.prefix, args.id)
  },
  checkAdminRole: async ({ options = {}, ...args }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = privateKey.sig(
      privateKey.key[eoaKey],
      ['bytes32', 'address', 'uint256', 'string'],
      ['0x00', args.account, _deadline, 'hello'],
    )
    const _hash = hash ?? _sig[1]
    const _signature = sig ? sig[0] : _sig[0]

    return args.accessCtrl.checkAdminRole(_hash, _deadline, _signature)
  },
  checkRole: async ({ options = {}, ...args }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, hash } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = privateKey.sig(
      privateKey.key[eoaKey],
      ['bytes32', 'address', 'uint256', 'string'],
      ['0x00', args.account, _deadline, 'hello'],
    )
    const _hash = hash ?? _sig[1]
    const _signature = sig ? sig[0] : _sig[0]

    return args.accessCtrl.checkRole(args.role, _hash, _deadline, _signature)
  },
  hasRole: async (args) => {
    return args.accessCtrl.hasRole(args.role, args.account)
  },
  addAdminRole: async ({ options = {}, ...args }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['address', 'uint256'], [args.account, _deadline])

    return args.accessCtrl.connect(args.from).addAdminRole(args.account, _deadline, _sig[0])
  },
  addRole: async ({ options = {}, ...args }) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ??
      privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'address', 'uint256'], [args.role, args.account, _deadline])

    return args.accessCtrl.connect(args.from).addRole(args.role, args.account, _deadline, _sig[0])
  },
  addRoleByProv: async ({ options = {}, ...args }) => {
    const { sender } = options
    const _from = sender ?? args.accounts[9]

    return args.accessCtrl.connect(_from).addRoleByProv(args.providerId, args.role, args.account)
  },
  addRoleByIssuer: async ({ options = {}, ...args }) => {
    const { sender } = options
    const _from = sender ?? args.accounts[9]

    return args.accessCtrl.connect(_from).addRoleByIssuer(args.issuerId, args.role, args.account)
  },
  addRoleByValidator: async ({ options = {}, ...args }) => {
    const { sender } = options
    const _from = sender ?? args.accounts[9]

    return args.accessCtrl.connect(_from).addRoleByValidator(args.validatorId, args.role, args.account)
  },
  delAdminRole: async ({ options = {}, ...args }) => {
    const { sender } = options
    const _from = sender ?? args.accounts[0]

    return args.accessCtrl.connect(_from).delAdminRole(args.account)
  },
  delProviderRole: async ({ options = {}, ...args }) => {
    const { sender } = options
    const _from = sender ?? args.accounts[0]

    return args.accessCtrl.connect(_from).delProviderRole(args.providerId, args.account)
  },
  delIssuerRole: async ({ options = {}, ...args }) => {
    const { sender } = options
    const _from = sender ?? args.accounts[0]

    return args.accessCtrl.connect(_from).delIssuerRole(args.issuerId, args.account)
  },
  delValidatorRole: async ({ options = {}, ...args }) => {
    const { sender } = options
    const _from = sender ?? args.accounts[0]

    return args.accessCtrl.connect(_from).delValidRole(args.validatorId, args.account)
  },
}
