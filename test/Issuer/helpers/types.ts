import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccountInstance,
  AddAccountIdOption,
  AddAccountRoleOption,
  AddBizZoneToIssuerOption,
  AddIssuerOption,
  AddIssuerRoleOption,
  BusinessZoneAccountInstance,
  ContractCallOption,
  ContractManagerInstance,
  CumulativeResetOption,
  FinancialCheckInstance,
  FinancialZoneAccountInstance,
  ForceBurnOption,
  IBCTokenInstance,
  IssuerCheckOption,
  IssuerInstance,
  ModIssuerOption,
  ModTokenLimitOption,
  PartialForceBurnOption,
  ProviderInstance,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'

export type IssuerContractType = {
  accounts: any
  issuer: IssuerInstance
  validator: ValidatorInstance
  provider: ProviderInstance
  account: AccountInstance
  token: TokenInstance
  ibcToken: IBCTokenInstance
  contractManager: ContractManagerInstance
  financialZoneAccount: FinancialZoneAccountInstance
  businessZoneAccount: BusinessZoneAccountInstance
  financialCheck: FinancialCheckInstance
}

export type IssuerType = { issuer: IssuerInstance }

export type BaseAmountType = IssuerType & {
  amount: number
}

export type BaseAccountsType = IssuerType & {
  accounts: SignerWithAddress[]
}

export type GetIssuerListType = IssuerType & {
  params: Parameters<IssuerInstance['getIssuerList']>
}

export type GetIssuerType = IssuerType & { params: Parameters<IssuerInstance['getIssuer']> }

export type GetIssuerIdType = IssuerType & {
  params: Parameters<IssuerInstance['getIssuerId']>
}

export type HasIssuerType = IssuerType & { params: Parameters<IssuerInstance['hasIssuer']> }

export type GetAccountListType = IssuerType & {
  params: Parameters<IssuerInstance['getAccountList']>
}

export type GetAccountType = IssuerType & { params: Parameters<IssuerInstance['getAccount']> }

export type HasAccountType = IssuerType & { params: Parameters<IssuerInstance['hasAccount']> }

export type CheckRoleType = IssuerType & {
  options?: Partial<IssuerCheckOption & ContractCallOption>
}

export type CheckMintType = BaseAmountType & {
  options?: Partial<IssuerCheckOption & ContractCallOption>
}

export type CheckBurnType = BaseAmountType & {
  options?: Partial<IssuerCheckOption & ContractCallOption>
}

export type AddIssuerType = BaseAccountsType & {
  options?: Partial<AddIssuerOption & ContractCallOption>
}

export type AddAccountIdType = BaseAccountsType & {
  options: Partial<AddAccountIdOption & ContractCallOption>
}

export type ModIssuerType = BaseAccountsType & {
  options: Partial<ModIssuerOption & ContractCallOption>
}

export type AddIssuerRoleType = BaseAccountsType & {
  options?: Partial<AddIssuerRoleOption & ContractCallOption>
}

export type ModTokenLimitType = BaseAccountsType & {
  options?: Partial<ModTokenLimitOption & ContractCallOption>
}

export type CumulativeResetType = BaseAccountsType & {
  options?: Partial<CumulativeResetOption & ContractCallOption>
}

export type SetAccountStatusType = BaseAccountsType & {
  accountStatus: string
  options?: Partial<CumulativeResetOption & ContractCallOption>
}

export type AddAccountRoleType = BaseAccountsType & {
  options?: Partial<AddAccountRoleOption & ContractCallOption>
}

export type ForceBurnType = BaseAccountsType & {
  options: Partial<ForceBurnOption & ContractCallOption>
}

export type PartialForceBurnType = BaseAccountsType & {
  options: Partial<PartialForceBurnOption & ContractCallOption>
}

export type AddBizZoneToIssuerType = BaseAccountsType & {
  options: Partial<AddBizZoneToIssuerOption & ContractCallOption>
}

export type DeleteBizZoneToIssuerType = AddBizZoneToIssuerType
