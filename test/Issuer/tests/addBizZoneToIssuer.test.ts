import { BASE, ERR } from '@test/common/consts'
import { AddBizZoneToIssuerOption, FinancialCheckInstance, IssuerInstance, ProviderInstance } from '@test/common/types'
import * as utils from '@test/common/utils'
import { issuerFuncs } from '@test/Issuer/helpers/function'

import '@nomicfoundation/hardhat-chai-matchers'
import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import { contractFixture } from '@test/common/contractFixture'
import { financialCheckFuncs } from '@test/FinancialCheck/helpers/function'
import { IssuerContractType } from '@test/Issuer/helpers/types'
import { providerFuncs } from '@test/Provider/helpers/function'
import { expect } from 'chai'
import { before } from 'mocha'

describe('addBizZoneToIssuer()', () => {
  let provider: ProviderInstance
  let issuer: IssuerInstance
  let financialCheck: FinancialCheckInstance
  let accounts: SignerWithAddress[]

  describe('正常系', () => {
    before(async () => {
      ;({ provider, accounts, issuer, financialCheck } = await contractFixture<IssuerContractType>())
    })

    describe('provider, issuer が登録されている状態', () => {
      const pramsAccounts = Object.values(BASE.ACCOUNT)
        .filter((v) => typeof v === 'object')
        .slice(0, 4)
      let params

      before(async () => {
        params = pramsAccounts.map((v, i) => {
          return {
            accountId: v.ID,
            accountName: v.NAME,
            accountEoa: accounts[i].getAddress(),
            accountApprovalAll: [{ spenderId: pramsAccounts[i].ID, amount: 100 }],
          }
        })
        await providerFuncs.addProvider({ provider: provider, accounts: accounts })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await providerFuncs.addToken({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuer({
          issuer: issuer,
          accounts: accounts,
          options: {
            issuerId: BASE.ISSUER.ISSUER1.ID,
            name: BASE.ISSUER.ISSUER1.NAME,
            bankCode: BASE.ISSUER.ISSUER1.BANK_CODE,
          },
        })
        await providerFuncs.addBizZone({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID2,
            zoneName: BASE.ZONE_NAME.NAME2,
          },
        })
      })
      it('issuerが登録されること', async () => {
        const params: AddBizZoneToIssuerOption = {
          issuerId: BASE.ISSUER.ISSUER0.ID,
          zoneId: BASE.ZONE_ID.ID2,
        }

        await issuerFuncs.addBizZoneToIssuer({ issuer: issuer, accounts: accounts, options: params })
        const result = await financialCheckFuncs.getIssuerWithZone({
          financialCheck: financialCheck,
          params: [BASE.ZONE_ID.ID2, 0, 100],
        })
        const expected = {
          issuers: [
            {
              issuerId: BASE.ISSUER.ISSUER0.ID,
              name: BASE.ISSUER.ISSUER0.NAME,
              bankCode: BASE.ISSUER.ISSUER0.BANK_CODE,
            },
          ],
          totalCount: 1,
          err: '',
        }
        result.issuers.forEach((v, i) => {
          utils.assertEqualForEachField(v, {
            issuerId: expected.issuers[i].issuerId,
            name: expected.issuers[i].name,
            bankCode: String(expected.issuers[i].bankCode),
          })
        })
      })
    })
  })

  describe('準正常系', () => {
    before(async () => {
      ;({ accounts, issuer } = await contractFixture<IssuerContractType>())
    })

    describe('初期状態', () => {
      it('Admin権限でない署名の場合、エラーがスローされること', async () => {
        await expect(
          issuerFuncs.addBizZoneToIssuer({ issuer: issuer, accounts: accounts, options: { eoaKey: BASE.EOA.ISSUER2 } }),
        ).to.be.revertedWith(ERR.ISSUER.ISSUER_NOT_ADMIN_ROLE)
      })

      it('署名期限切れの場合、エラーがスローされること', async () => {
        const exceededDeadline = await utils.getExceededDeadline()
        await expect(
          issuerFuncs.addBizZoneToIssuer({
            issuer: issuer,
            accounts: accounts,
            options: { deadline: exceededDeadline },
          }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_SIG_TIMEOUT)
      })

      it('署名無効の場合、エラーがスローされること', async () => {
        await expect(
          issuerFuncs.addBizZoneToIssuer({ issuer: issuer, accounts: accounts, options: { sig: ['0x1234', ''] } }),
        ).to.be.revertedWith(ERR.ACTRL.ACTRL_BAD_SIG)
      })
    })

    describe('IssuerRoleが登録されている状態', () => {
      before(async () => {
        await providerFuncs.addProvider({ provider: provider, accounts: accounts })
        await providerFuncs.addProviderRole({ provider: provider, accounts: accounts })
        await issuerFuncs.addIssuer({ issuer: issuer, accounts: accounts })
        await issuerFuncs.addIssuerRole({ issuer: issuer, accounts: accounts })
        await providerFuncs.addBizZone({
          provider: provider,
          accounts: accounts,
          options: {
            zoneId: BASE.ZONE_ID.ID1,
            zoneName: BASE.ZONE_NAME.NAME1,
          },
        })
      })

      it('存在しないゾーンを指定した場合、エラーがスローされること', async () => {
        const params: AddBizZoneToIssuerOption = {
          issuerId: BASE.ISSUER.ISSUER0.ID,
          zoneId: BASE.ZONE_ID.ID2,
        }

        await expect(
          issuerFuncs.addBizZoneToIssuer({ issuer: issuer, accounts: accounts, options: params }),
        ).to.be.revertedWith(ERR.PROV.ZONE_NOT_EXIST)
      })

      it('登録済のissuerIdを指定した場合、エラーがスローされること', async () => {
        const params: AddBizZoneToIssuerOption = {
          issuerId: BASE.ISSUER.ISSUER0.ID,
          zoneId: BASE.ZONE_ID.ID1,
        }

        await issuerFuncs.addBizZoneToIssuer({ issuer: issuer, accounts: accounts, options: params })
        await expect(
          issuerFuncs.addBizZoneToIssuer({ issuer: issuer, accounts: accounts, options: params }),
        ).to.be.revertedWith(ERR.ISSUER.ISSUER_ID_EXIST)
      })
    })
  })
})
