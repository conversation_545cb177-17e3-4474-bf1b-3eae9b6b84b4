import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccessCtrlInstance,
  AccountInstance,
  BusinessZoneAccountInstance,
  CheckTransactionTokenOption,
  ContractCallOption,
  ContractManagerInstance,
  FinancialCheckInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  TransferableMock1Instance,
  TransferableMock2Instance,
  TransferableMock3Instance,
  TransferProxyInstance,
  ValidatorInstance,
} from '@test/common/types'
import * as utils from '@test/common/utils'
import { PromiseType } from 'utility-types'

export type FinancialCheckContractType = {
  accounts: SignerWithAddress[]
  provider: ProviderInstance
  issuer: IssuerInstance
  validator: ValidatorInstance
  account: AccountInstance
  token: TokenInstance
  financialCheck: FinancialCheckInstance
  transferProxy: TransferProxyInstance
  contractManager: ContractManagerInstance
  customTransfer1: TransferableMock1Instance
  customTransfer2: TransferableMock2Instance
  customTransfer3: TransferableMock3Instance
  accessCtrl: AccessCtrlInstance
  ibcToken: IBCTokenInstance
  businessZoneAccount: BusinessZoneAccountInstance
}

export type FinancialCheckType = { financialCheck: FinancialCheckInstance }

export type CheckTransactionType = FinancialCheckType & {
  zoneId: number
  sendAccountId: string
  fromAccountId: string
  toAccountId: string
  fromAccountIssuerId: string
  amount: number
  sigInfo: PromiseType<ReturnType<typeof utils.siginfoGenerator>>
  options?: Partial<
    {
      accountSignature: Parameters<FinancialCheckInstance['checkTransaction']>[5]
    } & ContractCallOption
  >
  tokenOptions?: Partial<CheckTransactionTokenOption>
}

export type CheckExchangeType = FinancialCheckType & {
  accountId: string
  fromZoneId: number
  toZoneId: number
  amount: number
}

export type CheckSyncAccountType = FinancialCheckType & {
  validatorId: string
  accountId: string
  zoneId: number
  accountStatus: string
  sigInfo: PromiseType<ReturnType<typeof utils.siginfoGenerator>>
  options?: Partial<
    {
      accountSignature: Parameters<FinancialCheckInstance['checkTransaction']>[5]
    } & ContractCallOption
  >
}

export type CheckFinAccountStatusType = FinancialCheckType & {
  params: Parameters<FinancialCheckInstance['checkFinAccountStatus']>
}

export type GetAccountLimitType = FinancialCheckType & {
  params: Parameters<FinancialCheckInstance['getAccountLimit']>
}

export type GetBizZoneAccountStatusType = FinancialCheckType & {
  params: Parameters<FinancialCheckInstance['getBizZoneAccountStatus']>
}

export type GetIssuerWithZoneType = FinancialCheckType & {
  params: Parameters<FinancialCheckInstance['getIssuerWithZone']>
}
