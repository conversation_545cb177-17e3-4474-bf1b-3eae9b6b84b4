import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AddProviderOption,
  AddProviderRoleOption,
  AddTokenOption,
  CheckRoleOption,
  ContractCallOption,
  IssuerInstance,
  ModProviderOption,
  ModTokenOption,
  ModZoneOption,
  ProviderInstance,
  SetProviderAllOption,
  TokenInstance,
  ValidatorInstance,
} from '@test/common/types'

export type ProviderContractType = {
  accounts: SignerWithAddress[]
  provider: ProviderInstance
  validator: ValidatorInstance
  token: TokenInstance
  issuer: IssuerInstance
}

export type ProviderType = { provider: ProviderInstance }

export type BaseAccountsType = {
  provider: ProviderInstance
  accounts: SignerWithAddress[]
}
export type GetZoneNameType = ProviderType & {
  options: Parameters<ProviderInstance['getZoneName']>
}

export type GetTokenType = ProviderType & {
  options: Parameters<ProviderInstance['getToken']>
}

export type HasTokenType = ProviderType & {
  options: Parameters<ProviderInstance['hasToken']>
}

export type HasProviderType = ProviderType & {
  options: Parameters<ProviderInstance['hasProvider']>
}

export type GetAvailableIssuerIdsType = ProviderType & {
  options: Parameters<ProviderInstance['getAvailableIssuerIds']>
}

export type CheckAvailableIssuerIdsType = ProviderType & {
  options: Parameters<ProviderInstance['checkAvailableIssuerIds']>
}

export type AddProviderType = BaseAccountsType & {
  options?: Partial<AddProviderOption & ContractCallOption>
}

export type AddBizZoneType = BaseAccountsType & {
  options?: Partial<AddProviderOption & ContractCallOption>
}

export type AddProviderRoleType = BaseAccountsType & {
  options?: Partial<AddProviderRoleOption & ContractCallOption>
}

export type ModZoneType = BaseAccountsType & {
  options?: Partial<ModZoneOption & ContractCallOption>
}

export type AddTokenType = BaseAccountsType & {
  options?: Partial<AddTokenOption & ContractCallOption>
}

export type ModTokenType = BaseAccountsType & {
  options?: Partial<ModTokenOption & ContractCallOption>
}

export type ModProviderType = BaseAccountsType & {
  providerName: string
  options?: Partial<ModProviderOption & ContractCallOption>
}

export type CheckRoleType = ProviderType & {
  options?: Partial<CheckRoleOption & ContractCallOption>
}

export type GetProviderAllType = ProviderType & { providerId: string }

export type SetProviderAllType = {
  provider: ProviderInstance
  prams: any
  options?: Partial<SetProviderAllOption & ContractCallOption>
}
