import { BASE } from '@test/common/consts'
import { EventReturnType } from '@test/common/types'
import * as utils from '@test/common/utils'
import {
  AddBizZoneType,
  AddProviderRoleType,
  AddProviderType,
  AddTokenType,
  CheckAvailableIssuerIdsType,
  CheckRoleType,
  GetAvailableIssuerIdsType,
  GetProviderAllType,
  GetTokenType,
  GetZoneNameType,
  HasProviderType,
  HasTokenType,
  ModProviderType,
  ModTokenType,
  ModZoneType,
  ProviderType,
  SetProviderAllType,
} from './types'
import privateKey from '@/privateKey'

/**
 * providerのイベントを呼ぶ関数を持つobject
 */
export const providerFuncs = {
  version: ({ provider }: ProviderType) => {
    return provider.version()
  },
  getProvider: ({ provider }: ProviderType) => {
    return provider.getProvider() as unknown as Promise<EventReturnType['Provider']['GetProvider']>
  },
  getZone: ({ provider }: ProviderType) => {
    return provider.getZone() as unknown as Promise<EventReturnType['Provider']['GetZone']>
  },
  getZoneName: ({ provider, options }: GetZoneNameType) => {
    return provider.getZoneName(...options) as unknown as Promise<EventReturnType['Provider']['GetZoneName']>
  },
  getProviderCount: ({ provider }: ProviderType) => {
    return provider.getProviderCount() as unknown as Promise<EventReturnType['Provider']['GetProviderCount']>
  },
  getToken: ({ provider, options }: GetTokenType) => {
    return provider.getToken(...options) as unknown as Promise<EventReturnType['Provider']['GetToken']>
  },
  getTokenId: ({ provider }: ProviderType) => {
    return provider.getTokenId() as unknown as Promise<EventReturnType['Provider']['GetTokenId']>
  },
  getAvailableIssuerIds: ({ provider, options }: GetAvailableIssuerIdsType) => {
    return provider.getAvailableIssuerIds(...options) as unknown as Promise<
      EventReturnType['Provider']['GetAvailableIssuerIds']
    >
  },
  hasToken: ({ provider, options }: HasTokenType) => {
    return provider.hasToken(...options) as unknown as Promise<EventReturnType['Provider']['HasToken']>
  },
  hasProvider: ({ provider, options }: HasProviderType) => {
    return provider.hasProvider(...options) as unknown as Promise<EventReturnType['Provider']['HasProvider']>
  },
  addProvider: async ({ provider, accounts, options = {} }: AddProviderType) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ADMIN,
      providerId = BASE.PROV.PROV0.ID,
      zoneId = BASE.ZONE_ID.ID0,
      zoneName = BASE.ZONE_NAME.NAME0,
    } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ?? privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'uint16', 'uint256'], [providerId, zoneId, _deadline])

    return provider.connect(accounts[9]).addProvider(providerId, zoneId, zoneName, BASE.TRACE_ID, _deadline, _sig[0])
  },
  addBizZone: async ({ provider, accounts, options = {} }: AddBizZoneType) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ADMIN,
      zoneId = BASE.ZONE_ID.ID1,
      zoneName = BASE.ZONE_NAME.NAME1,
    } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ?? privateKey.sig(privateKey.key[eoaKey], ['uint16', 'string', 'uint256'], [zoneId, zoneName, _deadline])

    return provider.connect(accounts[9]).addBizZone(zoneId, zoneName, _deadline, _sig[0])
  },
  addProviderRole: async ({ provider, accounts, options = {} }: AddProviderRoleType) => {
    let { sig, deadline, eoaKey = BASE.EOA.ADMIN, providerId = BASE.PROV.PROV0.ID, providerEoa } = options
    providerEoa = providerEoa ?? (await accounts[BASE.EOA.PROV1].getAddress())
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ??
      privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'address', 'uint256'], [providerId, providerEoa, _deadline])

    return provider.connect(accounts[9]).addProviderRole(providerId, providerEoa, BASE.TRACE_ID, _deadline, _sig[0])
  },
  modZone: async ({ provider, accounts, options = {} }: ModZoneType) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.ADMIN,
      providerId = BASE.PROV.PROV0.ID,
      zoneName = BASE.ZONE_NAME.NAME0,
      traceId = BASE.TRACE_ID,
    } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig = sig ?? privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'uint256'], [providerId, _deadline])

    return provider.connect(accounts[9]).modZone(providerId, zoneName, traceId, _deadline, _sig[0])
  },
  addToken: async ({ provider, accounts, options = {} }: AddTokenType) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.PROV1,
      providerId = BASE.PROV.PROV0.ID,
      tokenId = BASE.TOKEN.TOKEN1.ID,
      name = BASE.TOKEN.TOKEN1.NAME,
      symbol = BASE.TOKEN.TOKEN1.SYMBOL,
    } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ??
      privateKey.sig(
        privateKey.key[eoaKey],
        ['uint256', 'uint256', 'bytes32', 'bytes32', 'uint256'],
        [providerId, tokenId, name, symbol, _deadline],
      )

    return provider.connect(accounts[9]).addToken(providerId, tokenId, name, symbol, BASE.TRACE_ID, _deadline, _sig[0])
  },
  modToken: async ({ provider, accounts, options = {} }: ModTokenType) => {
    const {
      sig,
      deadline,
      eoaKey = BASE.EOA.PROV1,
      tokenId = BASE.TOKEN.TOKEN1.ID,
      name = BASE.TOKEN.EMPTY,
      symbol = BASE.TOKEN.EMPTY,
    } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ??
      privateKey.sig(
        privateKey.key[eoaKey],
        ['bytes32', 'bytes32', 'bytes32', 'uint256'],
        [tokenId, name, symbol, _deadline],
      )

    return provider.connect(accounts[9]).modToken(tokenId, name, symbol, BASE.TRACE_ID, _deadline, _sig[0])
  },
  modProvider: async ({ provider, accounts, providerName, options = {} }: ModProviderType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN, providerId = BASE.PROV.PROV0.ID } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ??
      privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'bytes32', 'uint256'], [providerId, providerName, _deadline])

    return provider.connect(accounts[9]).modProvider(providerId, providerName, BASE.TRACE_ID, _deadline, _sig[0])
  },
  checkRole: async ({ provider, options = {} }: CheckRoleType) => {
    const { sig, deadline, eoaKey = BASE.EOA.PROV1, providerId = BASE.PROV.PROV0.ID } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ??
      privateKey.sig(privateKey.key[eoaKey], ['bytes32', 'string', 'uint256'], [providerId, 'morning', _deadline])

    return provider.checkRole(providerId, _sig[1], _deadline, _sig[0]) as unknown as Promise<
      EventReturnType['Provider']['CheckRole']
    >
  },
  checkAvailableIssuerIds: async ({ provider, options }: CheckAvailableIssuerIdsType) => {
    return provider.checkAvailableIssuerIds(...options) as unknown as Promise<
      EventReturnType['Provider']['CheckAvailableIssuerIds']
    >
  },
  getProviderAll: async ({ provider, providerId }: GetProviderAllType) => {
    return provider.getProviderAll(providerId) as unknown as Promise<EventReturnType['Provider']['GetProviderAll']>
  },
  setProviderAll: async ({ provider, prams, options = {} }: SetProviderAllType) => {
    const { sig, deadline, eoaKey = BASE.EOA.ADMIN } = options
    const _deadline = deadline ?? (await utils.getDeadline())
    const _sig =
      sig ?? privateKey.sig(privateKey.key[eoaKey], ['string', 'uint256'], [BASE.SALTS.SET_PROVIDER_ALL, _deadline])
    return provider.setProviderAll(prams, _deadline, _sig[0])
  },
}
