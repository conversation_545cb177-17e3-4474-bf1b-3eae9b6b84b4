/**
 * <PERSON><PERSON> Contracts Deployment Script
 * 1. ネットワーク: 'local'と'main'を'hardhat.config.ts'で設定
 * 2. ウォレット: 'local'の場合、'admin'と'deployer'はデフォルトで設定されるプライベートキーを利用する
 * 3. デプロイライブラリ: 'hardhat-deploy'を利用し、Proxyとして'OpenZeppelinTransparentProxy'を設定
 */
import { showDeployNetwork, showDeploySuccessWithMsg } from '@deploy/common/consoles'
import { deployContractWithSaveABI } from '@deploy/common/deployContractWithSaveABI'
import { handleTransaction } from '@deploy/common/handleTransaction'
import { kmsSignerProvider } from '@deploy/common/kmsSignerProvider'
import { saveChainId } from '@deploy/common/saveChainId'
import '@nomicfoundation/hardhat-ethers'
import 'hardhat-deploy'
import { DeployFunction } from 'hardhat-deploy/types'

const func: DeployFunction = async function () {
  showDeployNetwork({ title: 'IBC' })

  const kmsSigner = kmsSignerProvider()
  const deployerAddress = await kmsSigner.getAddress()
  console.log(`Deployer Address: ${deployerAddress}`)

  // Deploy IBCConnectionLib
  await deployContractWithSaveABI({
    contractName: 'IBCConnectionLib',
    options: { signer: kmsSigner },
  })

  // Deploy IBCChannelLib
  await deployContractWithSaveABI({
    contractName: 'IBCChannelLib',
    options: { signer: kmsSigner },
  })

  // Deploy IBCClient
  const ibcClient = await deployContractWithSaveABI({
    contractName: 'IBCClient',
    options: { signer: kmsSigner },
  })

  // Deploy IBCConnectionHandshake
  const ibcConnectionSelfStateNoValidation = await deployContractWithSaveABI({
    contractName: 'IBCConnectionSelfStateNoValidation',
    options: { signer: kmsSigner },
  })

  // Deploy IBCConnectionHandshake
  const ibcChannelHandshake = await deployContractWithSaveABI({
    contractName: 'IBCChannelHandshake',
    options: { signer: kmsSigner },
  })

  // Deploy IBCChannelPacketSendRecv
  const ibcChannelPacketSendRecv = await deployContractWithSaveABI({
    contractName: 'IBCChannelPacketSendRecv',
    options: { signer: kmsSigner },
  })

  // Deploy IBCChannelPacketTimeout
  const ibcChannelPacketTimeout = await deployContractWithSaveABI({
    contractName: 'IBCChannelPacketTimeout',
    options: { signer: kmsSigner },
  })

  // Deploy IBCHandler
  const ibcHandler = await deployContractWithSaveABI({
    contractName: 'OwnableIBCHandler',
    options: { signer: kmsSigner },
    deployArgs: [
      ibcClient.target,
      ibcConnectionSelfStateNoValidation.target,
      ibcChannelHandshake.target,
      ibcChannelPacketSendRecv.target,
      ibcChannelPacketTimeout.target,
    ],
    saveABIflag: true,
  })

  // Deploy IBFT2Client
  const ibft2Client = await deployContractWithSaveABI({
    contractName: 'IBFT2Client',
    options: { signer: kmsSigner },
    deployArgs: [ibcHandler.target],
  })

  // Deploy IBCMockApp
  const ibcMockApp = await deployContractWithSaveABI({
    contractName: 'IBCMockApp',
    options: { signer: kmsSigner },
    deployArgs: [ibcHandler.target],
  })

  console.log('********* Registering Client *********')
  await handleTransaction({
    transaction: ibcHandler.registerClient('hb-ibft2', ibft2Client.target),
  })

  console.log('********* Binding Port to MockApp *********')
  await handleTransaction({
    transaction: ibcHandler.bindPort('mockapp', ibcMockApp.target),
  })

  // Deploy情報をFormatしてPrintする
  showDeploySuccessWithMsg({
    deployerAddress,
    contractObj: {
      OwnableIBCHandler: ibcHandler.target,
      Ibft2Client: ibft2Client.target,
      IbcMockApp: ibcMockApp.target,
    },
  })
  // chainIdを保存
  await saveChainId()
}

export default func
func.tags = ['yui-contracts']
