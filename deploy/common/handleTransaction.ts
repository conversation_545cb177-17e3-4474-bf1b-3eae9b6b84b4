import * as Tools from '@tasks/common/tools'

type HandleTransactionProps = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  transaction: Promise<any>
  operationName?: string
}
export const handleTransaction = async ({ transaction, operationName }: HandleTransactionProps) => {
  try {
    const receipt = await transaction
    await receipt.wait().then((res) => {
      Tools.showEthersRes({ res })
    })
    return receipt
  } catch (error) {
    if (operationName) {
      console.log(`${operationName} failed:`)
    }
    console.log(error)
    throw error
  }
}
