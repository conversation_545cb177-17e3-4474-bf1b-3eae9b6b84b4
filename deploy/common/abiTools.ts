import fs from 'fs'
import path from 'path'
import { Contract } from 'ethers'

type SaveABIProps = {
  contractName: string
  contractInstance: Contract
  networkName: string
}
export const saveABI = async ({ contractName, contractInstance, networkName }: SaveABIProps) => {
  const outputDirectory = path.join(__dirname, `../../deployments/${networkName}`)

  // ディレクトリが存在しない場合は作成
  if (!fs.existsSync(outputDirectory)) {
    fs.mkdirSync(outputDirectory, { recursive: true })
  }

  // コントラクトの情報を取得
  const abi = contractInstance.interface.formatJson()
  await contractInstance
    .deploymentTransaction()
    ?.wait()
    .then((response) => {
      const data = {
        address: response?.contractAddress,
        abi: JSON.parse(abi as string),
        transactionHash: response?.hash,
        receipt: {
          to: response?.to,
          from: response?.from,
          contractAddress: response?.contractAddress,
          transactionIndex: response?.index,
          gasUsed: response?.gasUsed.toString(),
          logsBloom: response?.logsBloom,
          blockHash: response?.blockHash,
          blockNumber: response?.blockNumber,
          cumulativeGasUsed: response?.cumulativeGasUsed.toString(),
          status: response?.status,
        },
      }

      // ABIファイルのパス
      const abiFilePath = path.join(outputDirectory, `${contractName}.json`)

      // ABIファイルを保存
      fs.writeFileSync(abiFilePath, JSON.stringify(data, null, 2))
      console.log(`ABI saved to ${abiFilePath}`)
    })
}

type GetAddressFromABIProps = {
  network: string
  contractName: string
}
export async function getAddressFromABI({ network, contractName }: GetAddressFromABIProps) {
  // パスを動的に作成
  const filePath = path.join(__dirname, `../../deployments/${network}/${contractName}.json`)

  // ファイルが存在するか確認
  if (!fs.existsSync(filePath)) {
    throw new Error(`File not found: ${filePath}`)
  }

  const jsonContent = JSON.parse(fs.readFileSync(filePath, 'utf8'))

  // アドレスを取得
  const contractAddress = jsonContent.address

  console.log(`Contract address for ${network} network: ${contractAddress}`)
  return contractAddress
}
