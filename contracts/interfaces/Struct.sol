// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

/**
 * @dev プロバイダデータ。
 * 登録済かは_providerIdsをチェックするか、role!=0をチェックする。
 */
struct ProviderData {
    bytes32 role;
    bytes32 name;
    uint16 zoneId;
    bool enabled;
}

/**
 * @dev ゾーン情報。
 */
struct ZoneData {
    uint16 zoneId;
    string zoneName;
    bytes32[] availableIssuerIds;
}

/**
 * @dev 全てのプロバイダデータ。
 * バックアップリストア用データモデル
 */
struct ProviderAll {
    bytes32 providerId;
    ProviderData providerData;
    address providerEoa;
    ZoneData[] zoneData;
}

/**
 * @dev アカウントデータ。
 */
struct AccountData {
    /* アカウント名 */
    string accountName;
    /* アカウントステータス */
    bytes32 accountStatus;
    /* 紐づくビジネスゾーンID */
    uint16[] zoneIds;
    /* 残高 */
    uint256 balance;
    /* 停止理由コード */
    bytes32 reasonCode;
    /* 申込日時 */
    uint256 appliedAt;
    /* 登録日時 */
    uint256 registeredAt;
    /* 解約申込日時 */
    uint256 terminatingAt;
    /* 解約日時 */
    uint256 terminatedAt;
    /* 紐づくバリデータ */
    bytes32 validatorId;
}

/**
 * @dev アカウントデータ(zoneIdなし)
 */
struct AccountDataWithoutZoneId {
    /* アカウント名 */
    string accountName;
    /* アカウントステータス */
    bytes32 accountStatus;
    /* 残高 */
    uint256 balance;
    /* 停止理由コード */
    bytes32 reasonCode;
    /* 申込日時 */
    uint256 appliedAt;
    /* 登録日時 */
    uint256 registeredAt;
    /* 解約申込日時 */
    uint256 terminatingAt;
    /* 解約日時 */
    uint256 terminatedAt;
}

/**
 * @dev アカウントデータ(limit情報あり)
 */
struct AccountDataWithLimitData {
    /* アカウント名 */
    string accountName;
    /* アカウントステータス */
    bytes32 accountStatus;
    /* 残高 */
    uint256 balance;
    /* 停止理由コード */
    bytes32 reasonCode;
    /* 申込日時 */
    uint256 appliedAt;
    /* 登録日時 */
    uint256 registeredAt;
    /* 解約申込日時 */
    uint256 terminatingAt;
    /* 解約日時 */
    uint256 terminatedAt;
    /** 発行限度額 */
    uint256 mintLimit;
    /** 償却限度額 */
    uint256 burnLimit;
    /** チャージ限度額 */
    uint256 chargeLimit;
    /** 返還限度額 */
    uint256 dischargeLimit;
    /** 移転限度額 */
    uint256 transferLimit;
    /** 全取引の合計累積限度額 */
    uint256 cumulativeLimit;
    /** 一日の全取引累積額 */
    uint256 cumulativeAmount;
    /** 累積更新日 */
    uint256 cumulativeDate;
    /** 一日の各種累積限度額情報 */
    CumulativeTransactionLimits cumulativeTransactionLimits;
}

/**
 * @dev アカウントデータ(BizZoneアカウント情報あり)
 */
struct AccountDataAll {
    /* アカウント名 */
    string accountName;
    /* アカウントステータス */
    bytes32 accountStatus;
    /* 残高 */
    uint256 balance;
    /* 停止理由コード */
    bytes32 reasonCode;
    /* ゾーンID */
    uint16 zoneId;
    /* ゾーンID */
    string zoneName;
    /* 申込日時 */
    uint256 appliedAt;
    /* 登録日時 */
    uint256 registeredAt;
    /* 解約申込日時 */
    uint256 terminatingAt;
    /* 解約日時 */
    uint256 terminatedAt;
    /** 発行限度額 */
    uint256 mintLimit;
    /** 償却限度額 */
    uint256 burnLimit;
    /** チャージ限度額 */
    uint256 chargeLimit;
    /** 返還限度額 */
    uint256 dischargeLimit;
    /** 移転限度額 */
    uint256 transferLimit;
    /** 全取引の合計累積限度額 */
    uint256 cumulativeLimit;
    /** 一日の全取引累積額 */
    uint256 cumulativeAmount;
    /** 累積更新日 */
    uint256 cumulativeDate;
    /** 一日の各種累積限度額情報 */
    CumulativeTransactionLimits cumulativeTransactionLimits;
    /** ビジネスゾーンアカウント情報 */
    BusinessZoneAccountDataWithZoneId[] businessZoneAccounts;
}

/**
 * @dev アカウント限度額情報
 */
struct FinancialZoneAccountData {
    /** 発行限度額 */
    uint256 mintLimit;
    /** 償却限度額 */
    uint256 burnLimit;
    /** チャージ限度額 */
    uint256 chargeLimit;
    /** 返還限度額 */
    uint256 dischargeLimit;
    /** 移転限度額 */
    uint256 transferLimit;
    /** 全取引の合計累積限度額 */
    uint256 cumulativeLimit;
    /** 一日の全取引累積額 */
    uint256 cumulativeAmount;
    /** 累積更新日 */
    uint256 cumulativeDate;
    /** 一日の各種累積限度額情報 */
    CumulativeTransactionLimits cumulativeTransactionLimits;
}

/**
 * @dev 一日の各種累積限度額情報
 */
struct CumulativeTransactionLimits {
    /** 一日の発行累積限度額 */
    uint256 cumulativeMintLimit;
    /** 一日の発行累積額 */
    uint256 cumulativeMintAmount;
    /** 一日の償却累積限度額 */
    uint256 cumulativeBurnLimit;
    /** 一日の償却累積額 */
    uint256 cumulativeBurnAmount;
    /** 一日のチャージ累積限度額 */
    uint256 cumulativeChargeLimit;
    /** 一日のチャージ累積額 */
    uint256 cumulativeChargeAmount;
    /** 一日の返還累積限度額 */
    uint256 cumulativeDischargeLimit;
    /** 一日の返還累積額 */
    uint256 cumulativeDischargeAmount;
    /** 一日の移転累積限度額 */
    uint256 cumulativeTransferLimit;
    /** 一日の移転累積額 */
    uint256 cumulativeTransferAmount;
}

/**
 * @dev 全てのFinancialZoneAccount情報
 */
struct FinancialZoneAccountsAll {
    /** アカウントID */
    bytes32 accountId;
    /** アカウントデータ*/
    FinancialZoneAccountData financialZoneAccountData;
}

/**
 * @dev ビジネスゾーンアカウント情報
 */
struct BusinessZoneAccountData {
    /** アカウント表示名 */
    string accountName;
    /** 残高 */
    uint256 balance;
    /** アカウントステータス */
    bytes32 accountStatus;
    /* 申込日時 */
    uint256 appliedAt;
    /* 登録日時 */
    uint256 registeredAt;
    /* 解約申込日時 */
    uint256 terminatingAt;
    /* 解約日時 */
    uint256 terminatedAt;
}

/**
 * @dev ビジネスゾーンアカウント情報(zoneId付き)
 */
struct BusinessZoneAccountDataWithZoneId {
    /** アカウント表示名 */
    string accountName;
    /* ゾーンID */
    uint16 zoneId;
    /** ゾーン名 */
    string zoneName;
    /** 残高 */
    uint256 balance;
    /** アカウントステータス */
    bytes32 accountStatus;
    /* 申込日時 */
    uint256 appliedAt;
    /* 登録日時 */
    uint256 registeredAt;
    /* 解約申込日時 */
    uint256 terminatingAt;
    /* 解約日時 */
    uint256 terminatedAt;
}

/**
 * @dev 全てのBusinessZoneAccount情報
 */
struct BizAccountsAll {
    /** アカウントID */
    bytes32 accountId;
    /** 全てのビジネスゾーンアカウント情報 */
    BizAccountsByZoneId[] bizAccountsByZoneId;
}

/**
 * @dev BusinessZoneAccount
 */
struct BizAccountsByZoneId {
    /** ゾーンID */
    uint16 zoneId;
    /** ビジネスゾーンアカウント情報 */
    BusinessZoneAccountData businessZoneAccountData;
    /** アカウント存在確認 */
    bool accountIdExistenceByZoneId;
}

/**
 * @dev アカウント許可設定一覧
 */
struct AllowanceList {
    /** ownerIdに紐づくspenderIdの一覧 */
    bytes32[] spender;
    /** @dev 許可額設定(spencderId => AccountApproval) */
    mapping(bytes32 => AccountApproval) accountApprovalData;
}

/**
 * @dev アカウント許可設定情報
 */
struct AccountApproval {
    /** アカウント名 */
    string spenderAccountName;
    /** 許可額 */
    uint256 approvedAmount;
    /** 許可日時 */
    uint256 approvedAt;
}

/**
 * @dev アカウント全ての情報
 * バックアップリストア時利用
 */
struct AccountsAll {
    /* アカウントID */
    bytes32 accountId;
    /* アカウント名 */
    string accountName;
    /* アカウントステータス */
    bytes32 accountStatus;
    /* 紐づくビジネスゾーンID */
    uint16[] zoneIds;
    /* 残高 */
    uint256 balance;
    /* 停止理由コード */
    bytes32 reasonCode;
    /* 申込日時 */
    uint256 appliedAt;
    /* 登録日時 */
    uint256 registeredAt;
    /* 解約申込日時 */
    uint256 terminatingAt;
    /* 解約日時 */
    uint256 terminatedAt;
    /* 紐づくバリデータ */
    bytes32 validatorId;
    /* アカウント存在確認 */
    bool accountIdExistence;
    /* アカウント外部アドレス */
    address accountEoa;
    /* アカウントApprovalデータ */
    AccountApprovalAll[] accountApprovalAll;
}

struct AccountApprovalAll {
    /* spanderId */
    bytes32 spanderId;
    /* spanderアカウント名 */
    string spenderAccountName;
    /** 許可額 */
    uint256 allowanceAmount;
    /** 許可日時 */
    uint256 approvedAt;
}

/*
 * @dev 発行者データ。
 *      登録済みかどうかは_issuerIdsをチェックするか、role!=0をチェックする。
 */
struct IssuerData {
    /* 権限(登録済みデータの場合は非0) */
    bytes32 role;
    /* 発行者名 */
    string name;
    /* 金融機関コード */
    uint16 bankCode;
    /* アカウントIDリスト */
    bytes32[] accountIds;
}

/*
 * @dev 発行者が紐づくAccountsのデータ。
 *      getAccountList()の戻り値タイプ。
 */
struct IssuerAccountsData {
    bytes32 accountId;
    uint256 balance;
    bytes32 accountStatus;
    bytes32 reasonCode;
}

/*
 * @dev 発行者リストデータ。
 *      getIssuerList()の戻り値タイプ。
 */
struct IssuerListData {
    bytes32 issuerId;
    string name;
    uint16 bankCode;
}

/*
 * @dev 全発行者データ。
 *      バックアップリストア時のみ利用する。
 */
struct IssuerAll {
    /* 発行者ID */
    bytes32 issuerId;
    /* 権限(登録済みデータの場合は非0) */
    bytes32 role;
    /* 発行者名 */
    string name;
    /* 金融機関コード */
    uint16 bankCode;
    /* 発行者の存在管理マッピング */
    bool issuerIdExistence;
    /* 発行者外部所有アカウント */
    address issuerEoa;
    /* アカウントIDとアカウント存在マッピングリスト */
    IssuerAccountsExistence[] issuerAccountExistence;
}

struct IssuerAccountsExistence {
    /* Issuer配下のアカウントID */
    bytes32 accountId;
    /* IssuerごとのアカウントID存在確認マッピング */
    bool accountIdExistenceByIssuerId;
}

/**
 * @dev 検証者データ。
 *      登録済みかどうかは_validatorIdsをチェックする。
 */
struct ValidatorData {
    /* 検証者名 */
    bytes32 name;
    /* アカウントIDリスト */
    bytes32[] accountIds;
    /* 発行者ID */
    bytes32 issuerId;
    /* 権限(登録済みデータの場合は非0) */
    bytes32 role;
    /* 有効性 */
    bool enabled;
    /* バリデータ管理のアカウントID */
    bytes32 validatorAccountId;
}

/*
 * @dev 検証者が紐づくAccountsのデータ。
 *      getAccountList()の戻り値タイプ。
 */
struct ValidatorAccountsData {
    bytes32 accountId;
    string accountName;
    uint256 balance;
    bytes32 accountStatus;
    bytes32 reasonCode;
    uint256 appliedAt;
    uint256 registeredAt;
    uint256 terminatingAt;
    uint256 terminatedAt;
}

/*
 * @dev 検証者が紐づく全Accountsのデータ。
 *      getAccountAllList()の戻り値タイプ。
 */
struct ValidatorAccountsDataALL {
    bytes32 accountId;
    AccountDataAll accountDataAll;
}

/*
 * @dev 検証者リストデータ。
 *      getValidatorList()の戻り値タイプ。
 */
struct ValidatorListData {
    bytes32 validatorId;
    bytes32 name;
    bytes32 issuerId;
}

/*
 * @dev 全検証者データ。
 *      バックアップリストア時のみ利用する。
 */
struct ValidatorAll {
    /* 検証者ID */
    bytes32 validatorId;
    /* 検証者名 */
    bytes32 name;
    /* バリデータと紐づくイシュアID */
    bytes32 issuerId;
    /* 権限(登録済みデータの場合は非0) */
    bytes32 role;
    // /* バリデータと紐づくアカウントID */
    bytes32 validatorAccountId;
    /* 検証者有効状態 */
    bool enabled;
    /* 検証者の存在管理マッピング */
    bool validatorIdExistence;
    /* 紐付け済みイシュアIDのフラグマッピング */
    bool issuerIdLinkedFlag;
    /* 検証者外部所有アカウント */
    address validatorEoa;
    /* アカウントIDとアカウント存在マッピングリスト */
    ValidatorAccountsExistence[] validAccountExistence;
}

struct ValidatorAccountsExistence {
    /* バリデータ配下のアカウントID */
    bytes32 accountId;
    /* バリデータごとのアカウントID存在確認マッピング */
    bool accountIdExistenceByValidatorId;
}

/**
 * @dev トークンデータ。
 */
struct TokenData {
    /* 名前 */
    bytes32 name;
    /* symbol */
    bytes32 symbol;
    /* totalSupply */
    uint256 totalSupply;
    /* 有効 */
    bool enabled;
}

/**
 * @dev 全てのTokenデータ。
 */
struct TokenAll {
    /* TokenId */
    bytes32 tokenId;
    /* 名前 */
    bytes32 name;
    /* symbol */
    bytes32 symbol;
    /* totalSupply */
    uint256 totalSupply;
    /* 有効 */
    bool enabled;
}

/**
 * @dev _localTransfer()のリクエスト用のデータ(stack too deep対策)
 */
struct TransferData {
    bytes32 transferType; // トランザクションの種類（ex:charge, discharge）
    uint16 zoneId; // providerのゾーンID
    bytes32 fromValidatorId; // fromAccountIdが属するValidatorId
    bytes32 toValidatorId; // toAccountIdが属するValidatorId
    uint256 fromAccountBalance; // 送金元アカウントの現在残高
    uint256 toAccountBalance; // 送金先アカウントの現在残高
    uint256 businessZoneBalance; // exchange時のBizゾーンの現在残高
    uint16 bizZoneId; // 取引対象のビジネスゾーンID
    bytes32 sendAccountId; // 送金指示アカウントID
    bytes32 fromAccountId; // 送金元アカウントID
    string fromAccountName; // 送金元アカウント名
    bytes32 toAccountId; // 送金先アカウントID
    string toAccountName; // 送金先アカウント名
    uint256 amount; // 送金額
    bytes32 miscValue1; // カスタム領域1
    string miscValue2; // カスタム領域2
    string memo; // 送金実行時のメモ
}

/**
 * @dev burnCancel()のリクエスト用のデータ(stack too deep対策)
 */
struct BurnCancelData {
    bytes32 tokenId;
    bytes32 issuerId;
    bytes32 accountId;
    uint256 amount;
    uint256 blockTimestamp;
    uint256 deadline;
    bytes signature;
}

/**
 * @dev 各付加領域毎のアカウント残高とアカウント有効有無
 */
struct BalanceByZone {
    /* 領域毎のAccount残高 */
    uint256 balance;
    /* 領域毎の登録有無(false:未登録) */
    bool enabled;
    /* 付加領域の開設状況 */
    uint256 stateCode;
}

/**
 * @dev チャージ/ディスチャージ用の各種関数およびパケット用インターフェースstruct.
 */
struct SyncTokenTransferParams {
    bytes32 accountId;
    uint16 fromZoneId;
    uint16 toZoneId;
    uint256 amount;
    bytes32 transferType;
    bytes32 traceId;
}

/**
 * @dev アカウント申し込み/解約用の各種関数およびパケット用インターフェースstruct.
 */
struct SyncBusinessZoneAccountParams {
    bytes32 accountId;
    string accountName;
    uint16 fromZoneId;
    string fromZoneName;
    bytes32 accountStatus;
    bytes32 traceId;
}

/**
 * @dev BizZone内送金の残高更新のデータ
 */
struct SyncBuisinessZoneBlanaceParams {
    bytes32 fromAccountId;
    string fromAccountName;
    bytes32 toAccountId;
    string toAccountName;
    uint16 fromZoneId;
    uint256 amount;
    bytes32 traceId;
}

struct Config {
    string port;
    string channel;
    string version;
}

struct ForceDischarge {
    uint16 zoneId;
    uint256 dischargeAmount;
}

/**
 * @dev アカウント限度額の更新フラグ
 */
struct AccountLimitUpdates {
    /** 発行限度額の更新フラグ */
    bool mint;
    /** 償却限度額の更新フラグ */
    bool burn;
    /** チャージ限度額の更新フラグ */
    bool charge;
    /** 返還限度額の更新フラグ */
    bool discharge;
    /** 移転限度額の更新フラグ */
    bool transfer;
    /** 累積限度額の更新フラグ */
    CumulativeLimitUpdates cumulative;
}

/**
 * @dev アカウント一日の累積限度額の更新フラグ
 */
struct CumulativeLimitUpdates {
    /** 一日の全取引の合計累積限度額の更新フラグ */
    bool total;
    /** 一日の発行累積限度額の更新フラグ */
    bool mint;
    /** 一日の償却累積限度額の更新フラグ */
    bool burn;
    /** 一日のチャージ累積限度額の更新フラグ */
    bool charge;
    /** 一日の返還累積限度額の更新フラグ */
    bool discharge;
    /** 一日の移転累積限度額の更新フラグ */
    bool transfer;
}

/**
 * @dev アカウント限度額の値
 */
struct AccountLimitValues {
    /** 発行限度額 */
    uint256 mint;
    /** 償却限度額 */
    uint256 burn;
    /** チャージ限度額 */
    uint256 charge;
    /** 返還限度額 */
    uint256 discharge;
    /** 移転限度額 */
    uint256 transfer;
    /** 累積限度額 */
    CumulativeLimitValues cumulative;
}

/**
 * @dev アカウント一日の累積限度額の値
 */
struct CumulativeLimitValues {
    /** 一日の全取引の合計累積限度額 */
    uint256 total;
    /** 一日の発行累積限度額 */
    uint256 mint;
    /** 一日の償却累積限度額 */
    uint256 burn;
    /** 一日のチャージ累積限度額 */
    uint256 charge;
    /** 一日の返還累積限度額 */
    uint256 discharge;
    /** 一日の移転累積限度額 */
    uint256 transfer;
}

library Constant {
    /** @dev IBCApp名(AccountSyncBridge) */
    string internal constant _ACCOUNT_SYNC = "AccountSyncBridge";
    /** @dev IBCApp名(JPYTokenTransferBridge) */
    string internal constant _JPY_TOKEN_TRANSFER = "JPYTokenTransferBridge";
    /** @dev IBCApp名(BalanceSyncBridge) */
    string internal constant _BALANCE_SYNC = "BalanceSyncBridge";
    /** @dev 通常transfer識別用文字列 **/
    bytes32 internal constant _TRANSFER = "transfer";
    /** @dev カスタムtransfer識別用文字列 **/
    bytes32 internal constant _CUSTOM_TRANSFER = "custom_transfer";
    /** @dev チャージ識別用文字列 **/
    bytes32 internal constant _CHARGE = "charge";
    /** @dev ディスチャージ識別用文字列 **/
    bytes32 internal constant _DISCHARGE = "discharge";
    /** @dev Fin起点のディスチャージ識別用文字列 */
    bytes32 internal constant _DISCHARGE_FROM_FIN = "discharge_from_fin";
    /** @dev AccountStatus判定用の定数(口座申し込み) */
    bytes32 internal constant _STATUS_APPLIYNG = "applying";
    /** @dev AccountStatus判定用の定数(口座解約申し込み) */
    bytes32 internal constant _STATUS_TERMINATING = "terminating";
    /** @dev バリデーション用のアカウントステータス値(アクティブ) */
    bytes32 internal constant _STATUS_ACTIVE = "active";
    /** @dev バリデーション用のアカウントステータス値(凍結) */
    bytes32 internal constant _STATUS_FROZEN = "frozen";
    /** @dev バリデーション用のアカウントステータス値(解約済) */
    bytes32 internal constant _STATUS_TERMINATED = "terminated";
    /** @dev バリデーション用のアカウンステータス値(強制償却済) */
    bytes32 internal constant _STATUS_FORCE_BURNED = "force_burned";
    /** @dev FinZoneのID */
    uint16 internal constant _FINANCIAL_ZONE = 3000;
    /** @dev 未登録の場合にて返す空の値 **/
    bytes32 internal constant _EMPTY_VALUE = 0x00;
    /** @dev 未登録の場合にて返す空の値 **/
    string internal constant _EMPTY_STRING = "";
    /** @dev 未登録の場合にて返す空の件数 **/
    uint256 internal constant _EMPTY_LENGTH = 0;
    uint16 internal constant _EMPTY_UINT16 = 0;
    /** @dev 無制限の送金指示を許可する許可額の値 */
    uint256 internal constant _MAX_ALLOWANCE_VALUE = 999999999999999;
    /** @dev 無制限の累積限度額を許可する許可額の値 */
    uint256 internal constant _MAX_DAILY_LIMIT_VALUE = 999999999999999;
    /** @dev 無制限の各種限度額を許可する許可額の値 */
    uint256 internal constant _MAX_LIMIT_VALUE = 999999999999;
    /** @dev syncMint識別用文字列 */
    bytes32 internal constant _SYNC_MINT = "syncMint";
    /** @dev syncBurn識別用文字列 */
    bytes32 internal constant _SYNC_BURN = "syncBurn";
    /** @dev syncCharge識別用文字列 */
    bytes32 internal constant _SYNC_CHARGE = "syncCharge";
    /** @dev syncDischarge識別用文字列 */
    bytes32 internal constant _SYNC_DISCHARGE = "syncDischarge";
    /** @dev syncTransfer識別用文字列 */
    bytes32 internal constant _SYNC_TRANSFER = "syncTransfer";
    /** @dev リストの並び順の値 */
    string internal constant _SORT_DESC = "desc";
    /** @dev 累積額のリセット値として使用する定数 */
    uint256 internal constant _RESET_CUMULATIVE_AMOUNT = 0;
}
