// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/utils/math/SafeMathUpgradeable.sol";

import "./interfaces/IContractManager.sol";
import "./interfaces/Error.sol";
import "./interfaces/Struct.sol";

import "./libraries/BusinessZoneAccountLib.sol";
import "./remigration/RemigrationLib.sol";

/**
 * @dev BizinessZoneAccount情報管理コントラクト(FinZone用)
 */
contract BusinessZoneAccount is Initializable, IBusinessZoneAccount {
    using BusinessZoneAccountLib for *;

    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev ContractManagerアドレス */
    IContractManager private _contractManager;

    /** @dev アカウントデータ(zoneId => accountId => BusinessZoneAccountData) */
    mapping(uint16 => mapping(bytes32 => BusinessZoneAccountData)) private _businessZoneAccountData;

    /** @dev ビジネスゾーンごとのアカウントID存在確認フラグ */
    mapping(uint16 => mapping(bytes32 => bool)) private _accountIdExistenceByZoneId;

    /** @dev GET ALL関数の最大取得件数 */
    uint256 private constant _GET_BIZACCOUNTS_LIMIT = 1000;
    /** @dev getZoneIdsAllのsignature検証用 **/
    string private constant _GET_ZONEIDS_ALL_SIGNATURE = "getZoneIdsAll";
    /* @dev setZoneIdsAllのsignature検証用 */
    string private constant _SET_ZONEIDS_ALL_SIGNATURE = "setZoneIdsAll";
    /* @dev setBizAccountsAllのsignature検証用 */
    string private constant _SET_BIZACCOUNTS_ALL_SIGNATURE = "setBizAccountsAll";

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。
     *
     * @param contractManager contractManager
     */
    function initialize(IContractManager contractManager) public initializer {
        _contractManager = contractManager;
    }

    /**
     * @dev コントラクトバージョン取得。
     * @return version コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    /**
     * @dev BusinessZoneアカウント追加(CoreAPI/industry用)
     *
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param traceId トレースID
     */
    function setActiveBusinessAccountWithZone(
        uint16 zoneId,
        bytes32 accountId,
        bytes32 traceId
    ) external override {
        // validatorコントラクトからの呼び出しである事が条件
        require(msg.sender == address(_contractManager.validator()), Error.NOT_VALIDATOR_CONTRACT);

        //Account存在確認
        {
            (bool success, string memory errTmp) = _contractManager.account().hasAccount(accountId);
            require(success, errTmp);
        }

        // businessZoneAccountが既に登録されている＝申し込みが完了している ことを条件とする
        (bool success, string memory err) = _hasAccountByZone(zoneId, accountId);
        require(success, err);

        _businessZoneAccountData.setActivateAccount(zoneId, accountId);

        emit SetActiveBusinessAccountWithZone(accountId, zoneId, traceId);
    }

    /**
     * @dev BusinessZoneアカウント解約
     *
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     */
    function setBizZoneTerminated(uint16 zoneId, bytes32 accountId) external override {
        // validatorコントラクトからの呼び出しである事が条件
        require(msg.sender == address(_contractManager.validator()), Error.NOT_VALIDATOR_CONTRACT);

        //Account存在確認
        {
            (bool success, string memory errTmp) = _contractManager.account().hasAccount(accountId);
            require(success, errTmp);
        }

        // businessZoneAccountが既に登録されていることを条件とする
        (bool success, string memory err) = _hasAccountByZone(zoneId, accountId);
        require(success, err);

        _businessZoneAccountData.setBizZoneTerminated(zoneId, accountId);
    }

    /**
     * @dev ビジネスゾーンアカウントステータス更新申し込み(BizZoneアカウント申し込み含む)
     * ```
     * emit event: SyncBusinessZoneStatus()
     * ```
     * @param zoneId zoneId
     * @param zoneName zone名
     * @param accountId アカウントID
     * @param accountStatus アカウントステータス
     * @param traceId トレースID
     */
    function syncBusinessZoneStatus(
        uint16 zoneId,
        string memory zoneName,
        bytes32 accountId,
        string memory accountName,
        bytes32 accountStatus,
        bytes32 traceId
    ) external override {
        //IBCコントラクトからの呼び出しが条件
        require(
            msg.sender == address(_contractManager.ibcApp(Constant._ACCOUNT_SYNC)),
            Error.NOT_IBC_CONTRACT
        );

        // "applying"(登録申し込み)時に、アカウント未登録の場合、Bizアカウントのステータスのみを更新する
        // アカウントが登録済みの場合、Bizアカウントの情報を更新する
        if (
            accountStatus == Constant._STATUS_APPLIYNG &&
            _accountIdExistenceByZoneId[zoneId][accountId]
        ) {
            BusinessZoneAccountLib.setBusinessAccountStatus(
                _businessZoneAccountData,
                zoneId,
                accountId,
                accountStatus
            );
        } else {
            BusinessZoneAccountLib.syncBusinessZoneStatus(
                _businessZoneAccountData,
                _accountIdExistenceByZoneId,
                _contractManager,
                zoneId,
                accountId,
                accountName,
                accountStatus,
                traceId
            );
        }

        (bytes32 validatorId, ) = _contractManager.account().getValidatorIdByAccountId(accountId);

        emit SyncBusinessZoneStatus(
            validatorId,
            accountId,
            zoneId,
            zoneName,
            accountStatus,
            traceId
        );
    }

    /**
     * @dev ビジネスゾーンアカウントステータス更新
     * ```
     * emit event: SyncBusinessZoneBalance()
     * ```
     *
     * @param params BizZone内送金の残高更新のデータ
     */
    function syncBusinessZoneBalance(SyncBuisinessZoneBlanaceParams memory params)
        external
        override
    {
        // Tokenコントラクトからの呼び出しであることが条件
        require(
            (msg.sender == address(_contractManager.token()) ||
                msg.sender == address(_contractManager.ibcToken())),
            Error.NOT_TOKEN_CONTRACT
        );

        _businessZoneAccountData.syncBusinessZoneBalance(
            params.fromZoneId,
            params.toAccountId,
            params.fromAccountId,
            params.amount
        );

        BusinessZoneAccountData memory fromAccountData = _contractManager
            .businessZoneAccount()
            .getBusinessZoneAccount(params.fromZoneId, params.fromAccountId);

        BusinessZoneAccountData memory toAccountData = _contractManager
            .businessZoneAccount()
            .getBusinessZoneAccount(params.fromZoneId, params.toAccountId);

        // fromAccountIdからフィルタリング用のvalidatorIdを取得
        (bytes32 fromValidatorId, ) = _contractManager.account().getValidatorIdByAccountId(
            params.fromAccountId
        );

        (bytes32 toValidatorId, ) = _contractManager.account().getValidatorIdByAccountId(
            params.toAccountId
        );

        TransferData memory transferData = TransferData({
            transferType: Constant._TRANSFER,
            zoneId: params.fromZoneId,
            fromValidatorId: fromValidatorId,
            toValidatorId: toValidatorId,
            fromAccountBalance: fromAccountData.balance,
            toAccountBalance: toAccountData.balance,
            businessZoneBalance: Constant._EMPTY_LENGTH,
            bizZoneId: Constant._EMPTY_UINT16,
            sendAccountId: params.fromAccountId,
            fromAccountId: params.fromAccountId,
            fromAccountName: params.fromAccountName,
            toAccountId: params.toAccountId,
            toAccountName: params.toAccountName,
            amount: params.amount,
            miscValue1: Constant._EMPTY_VALUE,
            miscValue2: Constant._EMPTY_STRING,
            memo: ""
        });

        emit SyncBusinessZoneBalance(transferData, params.traceId);
    }

    /**
     * @dev ビジネスゾーン残高チャージ
     *
     * @param zoneId zoneId
     * @param accountId 送信先アカウントID
     * @param amount チャージ額
     */
    function addBusinessZoneBalance(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount
    ) external override {
        // Tokenコントラクトからの呼び出しであることが条件
        require(msg.sender == address(_contractManager.ibcToken()), Error.NOT_TOKEN_CONTRACT);

        _businessZoneAccountData.addBusinessZoneBalance(zoneId, accountId, amount);
    }

    /**
     * @dev ビジネスゾーン残高ディスチャージ
     *
     * @param zoneId zoneId
     * @param accountId 送信先アカウントID
     * @param amount チャージ額
     */
    function subtractBusinessZoneBalance(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount
    ) external override {
        // Tokenコントラクトからの呼び出しであることが条件
        require(
            (msg.sender == address(_contractManager.token()) ||
                msg.sender == address(_contractManager.ibcToken())),
            Error.NOT_TOKEN_CONTRACT
        );

        // 送信元アカウントの登録確認
        {
            (bool success, string memory err) = _hasAccountByZone(zoneId, accountId);
            require(success, err);
        }

        _businessZoneAccountData.subtractBusinessZoneBalance(zoneId, accountId, amount);
    }

    /**
     * @dev 全てのビジネスゾーンの残高を強制償却する
     * ゾーンの一覧をAccountコントラクトから取得し、取得した各ゾーンごとに以下の処理を行う
     * 各ゾーンの残高をbalanceUpdateByForceBurnを実行して0に更新する
     * 対象ゾーンの残高が0の場合はスキップする
     *
     *
     * @param accountId アカウントID
     * @return burnedAmount 償却した数量
     * @return forceDischarge ディスチャージしたBizゾーン情報
     */
    function forceBurnAllBalance(bytes32 accountId)
        external
        override
        returns (uint256 burnedAmount, ForceDischarge[] memory forceDischarge)
    {
        // Accountコントラクトからの呼び出しであることが条件
        require(msg.sender == address(_contractManager.account()), Error.NOT_ACCOUNT_CONTRACT);

        ZoneData[] memory zones = _contractManager.account().getZoneByAccountId(accountId);
        forceDischarge = new ForceDischarge[](zones.length);
        for (uint256 i = 0; i < zones.length; i++) {
            uint16 zoneId = zones[i].zoneId;
            // 指定のゾーンの残高を強制償却する
            uint256 dischargeAmount = _businessZoneAccountData.balanceUpdateByForceBurn(
                zoneId,
                accountId
            );
            burnedAmount += dischargeAmount;
            forceDischarge[i].zoneId = zoneId;
            forceDischarge[i].dischargeAmount = dischargeAmount;
        }
        return (burnedAmount, forceDischarge);
    }

    /**
     * @dev 残高を更新(償却)
     *
     * @param zoneId zoneId
     * @param accountId accountId
     * @param amount Burnする数量
     * @param traceId トレースID
     */
    function balanceUpdateByRedeemVoucher(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external override {
        // Tokenコントラクトからの呼び出しであることが条件
        require(msg.sender == address(_contractManager.token()), Error.NOT_TOKEN_CONTRACT);

        _businessZoneAccountData.balanceUpdateByRedeemVoucher(zoneId, accountId, amount);

        emit BalanceUpdateByRedeemVoucher(zoneId, accountId, amount, traceId);
    }

    /**
     * @dev 残高を更新(発行)
     * @param zoneId zoneId
     * @param accountId accountId
     * @param amount Burnする数量
     * @param traceId トレースID
     */
    function balanceUpdateByIssueVoucher(
        uint16 zoneId,
        bytes32 accountId,
        uint256 amount,
        bytes32 traceId
    ) external override {
        // Tokenコントラクトからの呼び出しであることが条件
        require(
            (msg.sender == address(_contractManager.token()) ||
                msg.sender == address(_contractManager.ibcToken())),
            Error.NOT_TOKEN_CONTRACT
        );

        _businessZoneAccountData.balanceUpdateByIssueVoucher(zoneId, accountId, amount);

        emit BalanceUpdateByIssueVoucher(zoneId, accountId, amount, traceId);
    }

    /**
     * @dev 指定されたAcountIdsに紐づくBusinessZoneAccounts情報を登録、もしくは上書きする
     *      バックアップリスト作業時のみ実行
     *
     * @param bizAccounts bizAccountInfo
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function setBizAccountsAll(
        BizAccountsAll memory bizAccounts,
        uint256 deadline,
        bytes memory signature
    ) external {
        // Admin権限を持つかチェック
        bytes32 hash = keccak256(abi.encode(_SET_BIZACCOUNTS_ALL_SIGNATURE, deadline));
        (bool has, string memory err) = _contractManager.accessCtrl().checkAdminRole(
            hash,
            deadline,
            signature
        );
        require(bytes(err).length == 0, err);
        require(has, Error.ACTRL_BAD_ROLE);

        RemigrationLib.setBizAccountsAll(
            _businessZoneAccountData,
            _accountIdExistenceByZoneId,
            bizAccounts
        );
    }

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev BizZoneのAccountId存在確認
     *
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return success true:存在する,false:存在しない
     * @return err エラーメッセージ
     */
    function hasAccountByZone(uint16 zoneId, bytes32 accountId)
        external
        view
        returns (bool success, string memory err)
    {
        return _hasAccountByZone(zoneId, accountId);
    }

    /**
     * @dev BizZoneのAccountId存在確認(内部関数)
     *
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return success true:存在する,false:存在しない
     * @return err エラーメッセージ
     */
    function _hasAccountByZone(uint16 zoneId, bytes32 accountId)
        internal
        view
        returns (bool success, string memory err)
    {
        if (zoneId == 0x00) {
            return (false, Error.ACCOUNT_INVALID_VAL);
        }
        if (accountId == 0x00) {
            return (false, Error.ACCOUNT_INVALID_VAL);
        }
        if (!this.accountIdExistenceByZoneId(zoneId, accountId)) {
            return (false, Error.ACCOUNT_ID_NOT_EXIST);
        }

        return (true, "");
    }

    /**
     * @dev BusinessZoneアカウント情報取得
     *
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return businessZoneAccountData
     */
    function getBusinessZoneAccount(uint16 zoneId, bytes32 accountId)
        external
        view
        returns (BusinessZoneAccountData memory)
    {
        return _businessZoneAccountData.getBusinessZoneAccountData(zoneId, accountId);
    }

    /**
     * @dev BusinessZoneアカウントのアクティブ状態確認
     *
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return success true:アクティブ状態,false:アクティブ以外
     */
    function isActivatedByZone(uint16 zoneId, bytes32 accountId)
        external
        view
        returns (bool success, string memory err)
    {
        (success, err) = _hasAccountByZone(zoneId, accountId);
        if (!success) {
            return (false, err);
        }
        if (_businessZoneAccountData[zoneId][accountId].accountStatus == Constant._STATUS_ACTIVE) {
            return (true, "");
        } else {
            return (false, Error.ACCOUNT_DISABLED);
        }
    }

    /**
     * @dev BusinessZoneアカウントの存在確認
     *
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @return success true:アクティブ状態,false:アクティブ以外
     */
    function accountIdExistenceByZoneId(uint16 zoneId, bytes32 accountId)
        external
        view
        returns (bool success)
    {
        return _accountIdExistenceByZoneId[zoneId][accountId];
    }

    /**
     * @dev limitとoffsetで指定したBusinessZoneAccountsを一括取得する
     *
     * @param index オフセット
     * @return bizAccounts 全BusinessZoneAccountsの情報
     */
    function getBizAccountsAll(uint256 index)
        external
        view
        override
        returns (BizAccountsAll memory bizAccounts)
    {
        return
            RemigrationLib.getBizAccountsAll(
                _businessZoneAccountData,
                _accountIdExistenceByZoneId,
                address(_contractManager),
                index
            );
    }
}
