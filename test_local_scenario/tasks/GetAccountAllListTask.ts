import { Network } from '../helpers/constants'
import { GetAccountAllListArguments } from '../helpers/task-arguments'
import { BaseTask } from './base-task'

export class GetAccountAllListTask extends BaseTask<GetAccountAllListArguments> {
  filePath = __filename

  constructor(network: Network) {
    super(network, 'getAccountAllList')
  }

  protected getDefaultArguments(): Partial<GetAccountAllListArguments> {
    return {
      validId: this.getNetworkConfig().VALID_ID,
      limit: '1000',
      offset: '0',
      printFullAccountInfo: '0'
    }
  }
}
