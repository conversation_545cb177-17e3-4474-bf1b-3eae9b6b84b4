import { Network } from "../helpers/constants";
import { PartialForceBurnTokenArguments } from "../helpers/task-arguments";
import { BaseTask } from "./base-task";

export class PartialForceBurnTokenTask extends BaseTask<PartialForceBurnTokenArguments> {
  filePath = __filename;

  constructor(network: Network) {
    super(network, "partialForceBurnToken");
  }

  protected getDefaultArguments(): Partial<PartialForceBurnTokenArguments> {
    const commonConfig = this.getCommonConfig();
    return {
      issuerKey: commonConfig.KEY_ISSUER
    };
  }
}