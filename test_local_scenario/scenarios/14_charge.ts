import { commonConfig, Network, networkConfig } from '../helpers/constants'
import { CheckExchangeTask } from '../tasks/CheckExchangeTask'
import { MintTokenTask } from '../tasks/MintTokenTask'
import { TransferTask } from '../tasks/TransferTask'
import { ERROR, extractAccountInfo, showAccountsStatus, SUCCESS } from './utils'

export async function charge(network: Network, accountId: string) {
  await new MintTokenTask(network).execute({
    accountId,
  })
  const checkExOutput = await new CheckExchangeTask(network).execute({
    accountId,
    toZoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
  })
  if (!checkExOutput.includes('result | ok')) {
    throw new Error('failed checkExchange.')
  }
  console.info('start charge to account3')
  const transferOutput = await new TransferTask(network).execute({
    accountId,
    toZoneId: networkConfig[Network.LocalFin].BIZ_ZONE_ID,
  })
  if (!transferOutput.includes(SUCCESS)) {
    return ERROR
  }
  await new Promise((resolve) => setTimeout(resolve, 15000))

  const accountInfo = extractAccountInfo(await showAccountsStatus(accountId))
  if (
    accountInfo.finZone.balance !== '4000' ||
    accountInfo.finZone.status !== commonConfig.STATUS_ACTIVE ||
    accountInfo.finZone.bizZoneAccountStatus !== commonConfig.STATUS_ACTIVE ||
    accountInfo.finZone.bizZoneAccountBalance !== '1000' ||
    accountInfo.bizZone.status !== commonConfig.STATUS_ACTIVE ||
    accountInfo.bizZone.balance !== '1000'
  ) {
    return ERROR
  }
  return SUCCESS
}
