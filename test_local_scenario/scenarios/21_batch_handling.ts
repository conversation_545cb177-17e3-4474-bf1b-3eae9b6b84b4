import { Network } from '../helpers/constants'
import { ERROR, ERROR_CODE, SUCCESS } from './utils'
import { GetAccountAllListTask } from '../tasks/GetAccountAllListTask'
import { registerValid } from './4_register_valid'
import { registerMassAccounts } from './6_register_account'
import { registerIssuer } from './3_register_issuer'

const issuerId = '121242'
const bankCode = '12341'
const validatorId = '567248'
const baseAccountId = '4104'

export async function registerMassAccount(network: Network, numberOfAccounts: number) {
  await registerIssuer(network, {
    issuerId: issuerId,
    bankCode: bankCode,
  })
  await registerValid(network, { validId: validatorId, issuerId: issuerId })
  const registerAccountsResult = await registerMassAccounts(network, {
    validId: validatorId,
    numberOfAccounts: String(numberOfAccounts),
    accountId: baseAccountId,
  })
  if (!registerAccountsResult.includes(`Successfully registered ${numberOfAccounts} accounts`)) {
    throw new Error('Failed to register mass accounts')
  }
}

export async function fetchMax1000Accounts(network: Network) {
  const overLimitResponse = await new GetAccountAllListTask(network).execute({ limit: '1001', validId: validatorId })
  const maxLimitResponse = await new GetAccountAllListTask(network).execute({ limit: '1000', validId: validatorId })
  if (
    overLimitResponse.includes(ERROR_CODE.ACCOUNT_TOO_LARGE_LIMIT) &&
    maxLimitResponse.includes('Elements Return: 1000')
  ) {
    return SUCCESS
  }
  return ERROR
}

export async function fetch1001stAccountOnSecondPage(network: Network) {
  const response = await new GetAccountAllListTask(network).execute({
    limit: '100',
    offset: '1000',
    validId: validatorId,
  })
  if (response.includes('Elements Return: 1')) return SUCCESS
  return ERROR
}

export async function fetch999Accounts(network: Network) {
  const response = await new GetAccountAllListTask(network).execute({ limit: '999', offset: '0', validId: validatorId })

  if (response.includes('Elements Return: 999')) return SUCCESS
  return ERROR
}
